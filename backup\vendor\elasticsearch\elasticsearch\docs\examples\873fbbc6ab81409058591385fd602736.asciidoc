// query-dsl/nested-query.asciidoc:165

[source, php]
----
$params = [
    'index' => 'drivers',
    'id' => '1',
    'body' => [
        'driver' => [
            'last_name' => 'McQueen',
            'vehicle' => [
                [
                    'make' => 'Powell Motors',
                    'model' => 'Canyonero',
                ],
                [
                    'make' => 'Miller-Meteor',
                    'model' => 'Ecto-1',
                ],
            ],
        ],
    ],
];
$response = $client->index($params);
$params = [
    'index' => 'drivers',
    'id' => '2',
    'body' => [
        'driver' => [
            'last_name' => '<PERSON>',
            'vehicle' => [
                [
                    'make' => 'Mifune',
                    'model' => 'Mach Five',
                ],
                [
                    'make' => 'Miller-Meteor',
                    'model' => 'Ecto-1',
                ],
            ],
        ],
    ],
];
$response = $client->index($params);
----
