[[operations]]
== Operations 

This page contains the information you need to perform various {es} operations 
by using the Client.

This section is a crash-course overview of the client and its syntax. If you 
are familiar with {es}, you'll notice that the methods are named just like REST 
endpoints.

You may also notice that the client is configured in a manner that facilitates 
easy discovery via your IDE. All core actions are available under the `$client` 
object (indexing, searching, getting, etc). Index and cluster management are 
located under the `$client->indices()` and `$client->cluster()` objects, 
respectively.

* <<index_management>>
* <<search_operations>>
* <<indexing_documents>>
* <<getting_documents>>
* <<updating_documents>>
* <<deleting_documents>>

include::index-operations.asciidoc[]

include::search-operations.asciidoc[]

include::crud.asciidoc[]