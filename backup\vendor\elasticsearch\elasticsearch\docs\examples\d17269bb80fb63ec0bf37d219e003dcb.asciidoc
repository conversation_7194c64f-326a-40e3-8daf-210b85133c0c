// search/request/sort.asciidoc:391

[source, php]
----
$params = [
    'body' => [
        'sort' => [
            [
                '_geo_distance' => [
                    'pin.location' => [
                        -70,
                        40,
                    ],
                    'order' => 'asc',
                    'unit' => 'km',
                    'mode' => 'min',
                    'distance_type' => 'arc',
                    'ignore_unmapped' => true,
                ],
            ],
        ],
        'query' => [
            'term' => [
                'user' => 'kimchy',
            ],
        ],
    ],
];
$response = $client->search($params);
----
