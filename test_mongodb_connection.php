<?php
/**
 * 測試 MongoDB 連接
 */

require 'vendor/autoload.php';

// 載入 .env
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$mongoUser = $_ENV['MONGO_USER'];
$mongoPass = $_ENV['MONGO_PASS'];
$mongoHost = $_ENV['MONGO_HOST'];
$mongoPort = $_ENV['MONGO_PORT'];
$mongoDb   = $_ENV['MONGO_DB'];
$mongoAuth = $_ENV['MONGO_AUTH_DB'];

$mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";

echo "測試 MongoDB 連接...\n";
echo "MongoDB URI: {$mongoUri}\n";

try {
    // 連接 MongoDB
    $client = new MongoDB\Client($mongoUri);
    
    // 測試連接
    $databases = $client->listDatabases();
    echo "✅ MongoDB 連接成功！\n";
    
    echo "可用的數據庫:\n";
    foreach ($databases as $database) {
        echo "- " . $database->getName() . "\n";
    }
    
    // 測試 users 集合
    $collection = $client->$mongoDb->users;
    $userCount = $collection->countDocuments();
    echo "\n📊 users 集合中的文檔數量: {$userCount}\n";
    
    if ($userCount > 0) {
        echo "✅ users 集合已存在，包含數據\n";
        
        // 顯示一個示例文檔
        $sampleUser = $collection->findOne();
        if ($sampleUser) {
            echo "\n📄 示例用戶文檔:\n";
            echo "Email: " . ($sampleUser['email'] ?? 'N/A') . "\n";
            echo "Status: " . ($sampleUser['status'] ?? 'N/A') . "\n";
            echo "Role: " . ($sampleUser['role'] ?? 'N/A') . "\n";
        }
    } else {
        echo "⚠️  users 集合為空，需要遷移數據\n";
    }
    
} catch (Exception $e) {
    echo "❌ MongoDB 連接失敗: " . $e->getMessage() . "\n";
    echo "請檢查:\n";
    echo "1. MongoDB 服務是否運行\n";
    echo "2. .env 文件中的連接參數是否正確\n";
    echo "3. 用戶權限是否足夠\n";
}
?>
