[[overview]]
== Overview

This is the official PHP client for {es}. It is designed to be a low-level 
client that does not stray from the REST API.

All methods closely match the REST API, and furthermore, match the method 
structure of other language clients (Ruby, Python, and so on). We hope that this 
consistency makes it easy to get started with a client and to seamlessly switch 
from one language to the next with minimal effort.

The client is designed to facilitate the API call using different way to read the
results using associative array, object, string or https://www.php-fig.org/psr/psr-7/[PSR-7].

Refer to the <<getting-started-php>> page for a step-by-step quick start with 
the PHP client.

[discrete]
[[psr-7-standard]]
=== PSR 7 standard

The {es} PHP client uses the https://www.php-fig.org/psr/[PSR] 7 standard. This 
standard is a community effort that contains a set of interfaces defined by the 
PHP Framework Interop Group. For more information, refer to the 
https://www.php-fig.org/psr/psr-7/[PSR 7 standard documentation].


[discrete]
[[version-compatibility]]
=== {es} and PHP version Compatibility

The {es} client is compatible with currently maintained PHP versions.

Language clients are forward compatible; meaning that clients support
communicating with greater or equal minor versions of {es} without breaking. It
does not mean that the client automatically supports new features of newer {es} 
versions; it is only possible after a release of a new client version. For
example, a 8.12 client version won't automatically support the new features of
the 8.13 version of {es}, the 8.13 client version is required for that.
{es} language clients are only backwards compatible with default distributions
and without guarantees made.

|===
| Elasticsearch Version | Elasticsearch-PHP Branch | Supported

| main                  | main                     | 
| 8.x                   | 8.x                      | 8.x
| 7.x                   | 7.x                      | 7.17
|===


* <<breaking_changes>>

include::breaking-changes.asciidoc[]