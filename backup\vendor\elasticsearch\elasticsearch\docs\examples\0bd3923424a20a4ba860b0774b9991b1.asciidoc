// query-dsl/nested-query.asciidoc:206

[source, php]
----
$params = [
    'index' => 'drivers',
    'body' => [
        'query' => [
            'nested' => [
                'path' => 'driver',
                'query' => [
                    'nested' => [
                        'path' => 'driver.vehicle',
                        'query' => [
                            'bool' => [
                                'must' => [
                                    [
                                        'match' => [
                                            'driver.vehicle.make' => 'Powell Motors',
                                        ],
                                    ],
                                    [
                                        'match' => [
                                            'driver.vehicle.model' => 'Canyonero',
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
];
$response = $client->search($params);
----
