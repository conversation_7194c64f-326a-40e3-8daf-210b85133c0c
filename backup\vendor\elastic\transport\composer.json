{"name": "elastic/transport", "type": "library", "description": "HTTP transport PHP library for Elastic products", "keywords": ["http", "transport", "elastic", "PSR-7", "PSR_17", "PSR-18"], "license": "MIT", "require": {"php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1 || ^2 || ^3", "php-http/discovery": "^1.14", "php-http/httplug": "^2.3", "composer-runtime-api": "^2.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "phpstan/phpstan": "^1.4", "php-http/mock-client": "^1.5", "nyholm/psr7": "^1.5"}, "autoload": {"psr-4": {"Elastic\\Transport\\": "src/"}}, "autoload-dev": {"psr-4": {"Elastic\\Transport\\Test\\": "tests/"}}, "scripts": {"test": ["vendor/bin/phpunit"], "phpstan": ["vendor/bin/phpstan analyse"]}, "config": {"allow-plugins": {"php-http/discovery": true}}}