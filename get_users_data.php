<?php

session_start();

// 添加調試信息
error_log("get_users_data.php called");
error_log("Session data: " . print_r($_SESSION, true));

// 檢查用戶是否已登錄
if (!isset($_SESSION['user_email'])) {
    error_log("User not logged in");
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// 檢查用戶權限
if ($_SESSION['user_role'] !== 'admin') {
    error_log("User role not admin: " . $_SESSION['user_role']);
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

// MongoDB 連接設置
require_once 'vendor/autoload.php';
require_once 'vendor/vlucas/phpdotenv/src/Dotenv.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$mongoHost = $_ENV['MONGO_HOST'];
$mongoPort = $_ENV['MONGO_PORT'];
$mongoUser = $_ENV['MONGO_USER'];
$mongoPass = $_ENV['MONGO_PASS']; // 保持特殊字符不變
$mongoDb = $_ENV['MONGO_DB_USER']; // 使用 optools 數據庫
$mongoAuth = $_ENV['MONGO_AUTH_DB'];

$mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";

try {
    $client = new MongoDB\Client($mongoUri);
    $collection = $client->$mongoDb->users;
    
    // 從 MongoDB 讀取所有用戶數據
    $cursor = $collection->find([], [
        'sort' => ['email' => 1] // 按 email 排序
    ]);
    
    $users = [];
    foreach ($cursor as $doc) {
        $user = [];
        foreach ($doc as $key => $value) {
            if ($value instanceof MongoDB\BSON\ObjectId) {
                $user[$key] = (string) $value;
            } elseif ($value instanceof MongoDB\BSON\UTCDateTime) {
                $user[$key] = $value->toDateTime()->format('Y-m-d H:i:s');
            } else {
                $user[$key] = $value;
            }
        }
        $users[] = $user;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $users
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
