// search/request/sort.asciidoc:262

[source, php]
----
$params = [
    'body' => [
        'query' => [
            'term' => [
                'product' => 'chocolate',
            ],
        ],
        'sort' => [
            [
                'offer.price' => [
                    'mode' => 'avg',
                    'order' => 'asc',
                    'nested' => [
                        'path' => 'offer',
                        'filter' => [
                            'term' => [
                                'offer.color' => 'blue',
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
];
$response = $client->search($params);
----
