// query-dsl/function-score-query.asciidoc:578

[source, php]
----
$params = [
    'body' => [
        'query' => [
            'function_score' => [
                'functions' => [
                    [
                        'gauss' => [
                            'price' => [
                                'origin' => '0',
                                'scale' => '20',
                            ],
                        ],
                    ],
                    [
                        'gauss' => [
                            'location' => [
                                'origin' => '11, 12',
                                'scale' => '2km',
                            ],
                        ],
                    ],
                ],
                'query' => [
                    'match' => [
                        'properties' => 'balcony',
                    ],
                ],
                'score_mode' => 'multiply',
            ],
        ],
    ],
];
$response = $client->search($params);
----
