[[connecting]]
== Connecting

This page contains the information you need to connect and use the Client with 
{es}.

**On this page**

* <<auth-ec, Elastic Cloud>>
* <<auth-http, Security by default (HTTPS)>>

[discrete]
[[auth-ec]]
=== Elastic Cloud

You can connect to https://www.elastic.co/cloud/[Elastic Cloud] using an **API key**
and a **Cloud ID**:

[source,php]
----
$client = ClientBuilder::create()
   ->setElasticCloudId('<cloud-id>')
   ->setApiKey('<api-key>')
   ->build();
----

Where <cloud-id> and <api-key> can be retrieved using the Elastic Cloud web UI.

You can get the `Cloud ID` from the `My deployment` page of your dashboard (see the red
rectangle reported in the screenshot).

image::images/cloud_id.png[alt="Elastic Cloud ID",align="center"]

You can generate an `API key` in the `Management` page under the section `Security`.

image::images/create_api_key.png[alt="Create API key",align="center"]

When you click on `Create API key` button you can choose a name and set the other
options (eg. restrict privileges, expire after time, etc).

image::images/api_key_name.png[alt="Choose an API name",align="center"]

After this step you will get the `API key`in the API keys page. 

image::images/cloud_api_key.png[alt="Cloud API key",align="center"]

**IMPORTANT**: you need to copy and store the `API key`in a secure place, since you will not
be able to view it again in Elastic Cloud.


[discrete]
[[auth-http]]
=== Security by default (HTTPS)

{es} 8.0 offers https://www.elastic.co/blog/introducing-simplified-elastic-stack-security[security by default],
that means it uses https://en.wikipedia.org/wiki/Transport_Layer_Security[TLS]
for protect the communication between client and server.

In order to configure `elasticsearch-php` for connecting to {es} 8.0 we
need to have the certificate authority file (CA).

You can install {es} in different ways, for instance using https://www.elastic.co/guide/en/elasticsearch/reference/current/docker.html[Docker]
you need to execute the followind command:

[source,shell]
--------------------------
docker pull docker.elastic.co/elasticsearch/elasticsearch:8.0.1
--------------------------

Once you have the docker image installed you can execute {es},
for instance using a single-node cluster configuration, as follows:

[source,shell]
--------------------------
docker network create elastic
docker run --name es01 --net elastic -p 9200:9200 -p 9300:9300 -it docker.elastic.co/elasticsearch/elasticsearch:8.0.1
--------------------------

This command creates an `elastic` Docker network and start {es}
using the port `9200` (default).

When you run the docker image a password is generated for the `elastic` user
and it's printed to the terminal (you might need to scroll back a bit in the terminal
to view it). You have to copy it since we will need to connect to {es}.

Now that {es} is running we can get the `http_ca.crt` file certificate.
We need to copy it from the docker instance, using the following command:

[source,shell]
--------------------------
docker cp es01:/usr/share/elasticsearch/config/certs/http_ca.crt .
--------------------------

Once we have the `http_ca.crt` certificate and the `password`, copied during the
start of {es} , we can use it to connect with `elasticsearch-php`
as follows:

[source,php]
--------------------------
$client = ClientBuilder::create()
    ->setHosts(['https://localhost:9200'])
    ->setBasicAuthentication('elastic', 'password copied during Elasticsearch start')
    ->setCABundle('path/to/http_ca.crt')
    ->build();
--------------------------

For more information about the Docker configuration of Elasticsearch you can
read the official documentation https://www.elastic.co/guide/en/elasticsearch/reference/current/docker.html[here].

