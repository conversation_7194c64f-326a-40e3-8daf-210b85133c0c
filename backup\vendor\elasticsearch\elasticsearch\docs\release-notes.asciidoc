[[release-notes]]
== Release notes

* <<rn-8-14-0>>
* <<rn-8-13-0>>
* <<rn-8-12-0>>
* <<rn-8-11-0>>
* <<rn-8-10-0>>
* <<rn-8-9-0>>
* <<rn-8-8-0>>
* <<rn-8-7-0>>
* <<rn-8-6-1>>
* <<rn-8-5-0>>
* <<rn-8-4-0>>
* <<rn-8-3-0>>
* <<rn-8-2-0>>
* <<rn-8-1-0>>
* <<rn-8-0-1>>
* <<rn-8-0-0>>
* <<rn-7-17-0>>
* <<rn-7-16-0>>
* <<rn-7-15-0>>
* <<rn-7-14-0>>
* <<rn-7-13-1>>
* <<rn-7-13-0>>
* <<rn-7-12-0>>
* <<rn-7-11-0>>
* <<rn-7-10-0>>
* <<rn-7-9-1>>
* <<rn-7-9-0>>
* <<rn-7-8-0>>
* <<rn-7-7-0>>
* <<rn-7-6-1>>
* <<rn-7-6-0>>
* <<rn-7-5-1>>
* <<rn-7-5-0>>
* <<rn-7-4-1>>
* <<rn-7-4-0>>
* <<rn-7-3-0>>
* <<rn-7-2-2>>
* <<rn-7-2-1>>
* <<rn-7-2-0>>
* <<rn-7-1-1>>
* <<rn-7-1-0>>
* <<rn-7-0-2>>
* <<rn-7-0-1>>
* <<rn-7-0-0>>

[discrete]
[[rn-8-14-0]]
=== 8.14.0

* Updated the API endpoints to Elasticserach 8.14.0

[discrete]
[[rn-8-13-0]]
=== 8.13.0

* Updated the API endpoints to Elasticserach 8.13.0
* Added the Added the mapTo($class) function to Elasticsearch response for mapping the result
  of https://www.elastic.co/guide/en/elasticsearch/reference/current/esql.html[ES|QL]
  query to an object of stdClass or a specific class https://github.com/elastic/elasticsearch-php/issues/1398[#1398]

[discrete]
[[rn-8-12-0]]
=== 8.12.0

* Updated the API endpoints to Elasticserach 8.12.0
* Tested the library with PHP 8.3

[discrete]
[[rn-8-11-0]]
=== 8.11.0

* Updated the API endpoints to Elasticserach 8.11.0
* Fixed path in hosts configuration is ignored
  https://github.com/elastic/elasticsearch-php/issues/1377[#1377]
  

[discrete]
[[rn-8-10-0]]
=== 8.10.0

* Updated the API endpoints to Elasticserach 8.10.0

[discrete]
[[rn-8-9-0]]
=== 8.9.0

* Updated the API endpoints to Elasticserach 8.9.0
* Fixed issue with psr/http-message, changed PSR-7 versions to 1.1 and 2.0
  https://github.com/elastic/elasticsearch-php/pull/1344[#1344]

[discrete]
[[rn-8-8-0]]
=== 8.8.0

* Updated the API endpoints to Elasticserach 8.8.0
* Added SearchHitIterators and SearchResponseIterator helpers revised with new version 
  https://github.com/elastic/elasticsearch-php/pull/1302[#1302]

[discrete]
[[rn-8-7-0]]
=== 8.7.0

* Updated the API endpoints to Elasticserach 8.7.0
* Allow plugin for `php-http/discovery` library
  https://github.com/elastic/elasticsearch-php/pull/1294[#1294]

[discrete]
[[rn-8-6-1]]
=== 8.6.1

* Updated the API endpoints to Elasticserach 8.6.0

[discrete]
[[rn-8-5-0]]
=== 8.5.0

* Updated the API endpoints to Elasticserach 8.5.0

[discrete]
[[rn-8-4-0]]
=== 8.4.0

* Added a `ClientInterface` to simplify the mock of the Client,
  this is a fix for https://github.com/elastic/elasticsearch-php/issues/1227[#1227]
  https://github.com/elastic/elasticsearch-php/pull/1249[#1249]
* Added the support of Symfony HTTP client, fixing the issue https://github.com/elastic/elasticsearch-php/issues/1241[#1241]
  https://github.com/elastic/elasticsearch-php/pull/1243[#1243]
* Added the API compatibility header
  https://github.com/elastic/elasticsearch-php/pull/1233[#1233]
* Updated the API endpoints to Elasticserach 8.4.0

[discrete]
[[rn-8-3-0]]
=== 8.3.0

* Updated the API endpoints to Elasticserach 8.3.0

[discrete]
[[rn-8-2-0]]
=== 8.2.0

* Updated the API endpoints to Elasticserach 8.2.0
* Added the array support for `text/plain` 
  https://github.com/elastic/elasticsearch-php/pull/1220[#1220]

[discrete]
[[rn-8-1-0]]
=== 8.1.0

* Updated the API endpoints to Elasticserach 8.1.0

[discrete]
[[rn-8-0-1]]
=== 8.0.1

* use of `NoNodeAvailableException` exception in endpoints, fixing
  https://github.com/elastic/elasticsearch-php/issues/1209[#1209]
  

[discrete]
[[rn-8-0-0]]
=== 8.0.0

This new major version of elasticsearch-php contains a brand new implementation
compared with 7.x. It supports https://www.php-fig.org/psr/psr-7/[PSR-7] for HTTP
messages and https://www.php-fig.org/psr/psr-18/[PSR-18] for HTTP client
communications. We used the https://github.com/elastic/elastic-transport-php[elastic-transport-php]
library for HTTP communications.

We tried to reduce the BC breaks as much as possible with 7.x but there are some
(big) differences:

* we changed the namespace, now everything is under `Elastic\Elasticsearch`;
* we changed the Exception model, using the namespace `Elastic\Elasticsearch\Exception`.
  All the exceptions extends the `ElasticsearchException` interface, as in 7.x;
* we changed the response type of each endpoints using an Elasticsearch response class.
  This class wraps a a PSR-7 response allowing the access of the body response
  as array or object. This means you can access the API response as in 7.x, no BC break here!

You can have a look at the https://github.com/elastic/elasticsearch-php/blob/8.0/BREAKING_CHANGES.md[BREAKING_CHANGES]
online document for more information.


[discrete]
[[rn-7-17-0]]
=== 7.17.0

* Allow psr/log v3
  https://github.com/elastic/elasticsearch-php/pull/1184[#1184]


[discrete]
[[rn-7-16-0]]
=== 7.16.0

* Added support of includePortInHostHeader in ClientBuilder::fromConfig
  https://github.com/elastic/elasticsearch-php/pull/1181[#1181]
* Fixed UTF-16 issue in SmartSerializer with single unpaired surrogate in unicode escape
  https://github.com/elastic/elasticsearch-php/pull/1179[#1179]
* Replace trait with abstract class to avoid Deprecated Functionality issue in PHP 8.1
  https://github.com/elastic/elasticsearch-php/pull/1175[#1175]


[discrete]
[[rn-7-15-0]]
=== 7.15.0

* Updated endpoints for Elasticsearch 7.15.0
  https://github.com/elastic/elasticsearch-php/commit/995f6d4bde7de76004e95d7a434b1d59da7a7e75[995f6d4]


[discrete]
[[rn-7-14-0]]
=== 7.14.0

* Usage of psr/log version 2 
  https://github.com/elastic/elasticsearch-php/pull/1154[#1154]
* Update search iterators to send `scroll_id` inside the request body
  https://github.com/elastic/elasticsearch-php/pull/1134[#1134]
* Added the `ingest.geoip.downloader.enabled=false` setting for ES
  https://github.com/elastic/elasticsearch-php/commit/586735109dc18f22bfdf3b73ab0621b37e857be1[5867351]
* Removed phpcs for autogenerated files (endpoints)
  https://github.com/elastic/elasticsearch-php/commit/651c57b2e6bf98a0fd48220949966e630e5a804a[651c57b]


[discrete]
[[rn-7-13-1]]
=== 7.13.1

* Added port in url for trace and logger messages
  https://github.com/elastic/elasticsearch-php/pull/1126[#1126]


[discrete]
[[rn-7-13-0]]
=== 7.13.0

* (DOCS) Added the HTTP meta data section
  https://github.com/elastic/elasticsearch-php/pull/1143[#1143]
* Added support for API Compatibility Header
  https://github.com/elastic/elasticsearch-php/pull/1142[#1142]
* (DOCS) Added Helpers section to PHP book
  https://github.com/elastic/elasticsearch-php/pull/1129[#1129]
* Added the API description in phpdoc section for each endpoint
  https://github.com/elastic/elasticsearch-php/commit/9e05c8108b638b60cc676b6a4f4be97c7df9eb64[9e05c81]
* Usage of PHPUnit 9 only + migrated xml configurations
  https://github.com/elastic/elasticsearch-php/commit/038b5dd043dc76b20b9f5f265ea914a38d33568d[038b5dd]

  
[discrete]
[[rn-7-12-0]]
=== 7.12.0

* Updated the endpoints for ES 7.12 + removed `cpliakas/git-wrapper` in favor of 
  `symplify/git-wrapper`
  https://github.com/elastic/elasticsearch-php/commit/136d5b9717b3806c6b34ef8a5076bfe7cee8b46e[136d5b9]
* Fixed warning header as array in YAML tests generator
  https://github.com/elastic/elasticsearch-php/commit/0d81be131bfc7eff6ef82468e61c16077a892aab[0d81be1]
* Refactored TEST_SUITE with free, platinum + removed old YamlRunnerTest
  https://github.com/elastic/elasticsearch-php/commit/f69d96fc283580177002b4088c279c3d0c07befe[f69d96f]
  

[discrete]
[[rn-7-11-0]]
=== 7.11.0

* Added the `X-Elastic-Client-Meta` header which is used by Elastic Cloud and 
  can be disabled with `ClientBuilder::setElasticMetaHeader(false)`
  https://github.com/elastic/elasticsearch-php/pull/1089[#1089]
* Replaced `array_walk` with `array_map` in `Connection::getURI` for PHP 8
  compatibility
  https://github.com/elastic/elasticsearch-php/pull/1075[#1075]
* Remove unnecessary `InvalidArgumentExceptions`
  https://github.com/elastic/elasticsearch-php/pull/1069[#1069]
* Introducing PHP 8 compatibility
  https://github.com/elastic/elasticsearch-php/pull/1063[#1063]
* Replace Sami by Doctum and fix `.gitignore`
  https://github.com/elastic/elasticsearch-php/pull/1062[#1062]


[discrete]
[[rn-7-10-0]]
=== 7.10.0

* Updated endpoints and namespaces for {es} 7.10
  https://github.com/elastic/elasticsearch-php/commit/3ceb7484a111aa20126168460c79f098c4fe0792[3ceb748]
* Fixed ClientBuilder::fromConfig allowing multiple function parameters (for 
  example, `setApiKey`)
  https://github.com/elastic/elasticsearch-php/pull/1076[#1076]
* Refactored the YAML tests using generated PHPUnit code
  [85fadc2](https://github.com/elastic/elasticsearch-php/commit/85fadc2bd4b2b309b19761a50ff13010d43a524d)


[discrete]
[[rn-7-9-1]]
=== 7.9.1

* Fixed using object instead of array in onFailure transport event
  https://github.com/elastic/elasticsearch-php/pull/1066[#1066]
* Fixed reset custom header after endpoint call
  https://github.com/elastic/elasticsearch-php/pull/1065[#1065]
* Show generic error messages when server returns no response
  https://github.com/elastic/elasticsearch-php/pull/1056[#1056]


[discrete]
[[rn-7-9-0]]
=== 7.9.0

* Updated endpoints and namespaces for {es} 7.9
  https://github.com/elastic/elasticsearch-php/commit/28bf0ed6df6bc95f83f369509431d97907bfdeb0[28bf0ed]
* Moved `scroll_id` into `body` for search operations in the documentation
  https://github.com/elastic/elasticsearch-php/pull/1052[#1052]
* Fixed PHP 7.4 preloading feature for autoload.php
  https://github.com/elastic/elasticsearch-php/pull/1051[#1051]
* Improved message of JSON errors using `json_last_error_msg()`
  https://github.com/elastic/elasticsearch-php/pull/1045[#1045]

  
[discrete]
[[rn-7-8-0]]
=== 7.8.0

* Updated endpoints and namespaces for {es} 7.8
  https://github.com/elastic/elasticsearch-php/commit/f2a0828d5ee9d126ad63e2a1d43f70b4013845e2[f2a0828]
* Improved documentation
  https://github.com/elastic/elasticsearch-php/pull/1038[#1038], 
  https://github.com/elastic/elasticsearch-php/pull/1027[#1027], 
  https://github.com/elastic/elasticsearch-php/pull/1025[#1025]


[discrete]
[[rn-7-7-0]]
=== 7.7.0

* Removed setId() into endpoints, fixed `util/GenerateEndpoints.php`
  https://github.com/elastic/elasticsearch-php/pull/1026[#1026]
* Fixes JsonErrorException with code instead of message
  https://github.com/elastic/elasticsearch-php/pull/1022[#1022]
* Better exception message for Could not parse URI
  https://github.com/elastic/elasticsearch-php/pull/1016[#1016]
* Added JUnit log for PHPUnit
  https://github.com/elastic/elasticsearch-php/commit/88b7e1ce80a5a52c1d64d00c55fef77097bbd8a9[88b7e1c]
* Added the XPack endpoints
  https://github.com/elastic/elasticsearch-php/commit/763d91a3d506075316b84a38b2bed7a098da5028[763d91a]



[discrete]
[[rn-7-6-1]]
=== 7.6.1

* Fixed issue with `guzzlehttp/ringphp` and `guzzle/streams` using forks 
  `ezimuel/ringphp` and `ezimuel/guzzlestreams`
  https://github.com/elastic/elasticsearch-php/commit/92a6a4adda5eafd1823c7c9c386e2c7e5e75cd08[92a6a4a]


[discrete]
[[rn-7-6-0]]
=== 7.6.0

* Generated the new endpoints for {es} 7.6.0
  https://github.com/elastic/elasticsearch-php/commit/be31f317af704f333b43bbcc7c01ddc7c91ec6f8[be31f31]


[discrete]
[[rn-7-5-1]]
=== 7.5.1

* Fixes port missing in log https://github.com/elastic/elasticsearch-php/issues/925[#925] 
  https://github.com/elastic/elasticsearch-php/commit/125594b40d167ef1509b3ee49a3f93426390c426[75e0888]
* Added `ClientBuilder::includePortInHostHeader()` to add the `port` in the 
  `Host` header. This fixes https://github.com/elastic/elasticsearch-php/issues/993[#993].
  By default the `port` is not included in the `Host` header.
  https://github.com/elastic/elasticsearch-php/pull/997[#997]
* Replace abandoned packages: ringphp, streams and phpstan-shim 
  https://github.com/elastic/elasticsearch-php/pull/996[#996]
* Fixed gzip compression when setting Cloud Id
  https://github.com/elastic/elasticsearch-php/pull/986[#986]


[discrete]
[[rn-7-5-0]]
=== 7.5.0

* Fixed `Client::extractArgument` iterable casting to array; this allows passing 
  a `Traversable` body for some endpoints (for example, Bulk, Msearch, 
  MsearchTemplate) 
  https://github.com/elastic/elasticsearch-php/pull/983[#983]
* Fixed the Response Exception if the `reason` field is null
  https://github.com/elastic/elasticsearch-php/pull/980[#980]
* Added support for PHP 7.4
  https://github.com/elastic/elasticsearch-php/pull/976[#976]


[discrete]
[[rn-7-4-1]]
=== 7.4.1

* We added the suppress operator `@` for the deprecation messages 
  `@trigger_error()`. With this approach, we don't break existing application 
  that convert PHP errors in Exception (for example, using Laravel with issue 
  https://github.com/babenkoivan/scout-elasticsearch-driver/issues/297[297])
  Using the `@` operator is still possible to intercept the deprecation message 
  using a custom error handler.
  https://github.com/elastic/elasticsearch-php/pull/973[#973]
* Add missing leading slash in the URL of put mapping endpoint
  https://github.com/elastic/elasticsearch-php/pull/970[#970]
* Fix pre 7.2 endpoint class name with aliases + reapply fix #947. This PR 
  solved the unexpected BC break introduce in 7.4.0 with the code
  generation tool
  https://github.com/elastic/elasticsearch-php/pull/968[#968]


[discrete]
[[rn-7-4-0]]
=== 7.4.0

* Added the code generation for endpoints and namespaces based on the 
  https://github.com/elastic/elasticsearch/tree/v7.4.2/rest-api-spec/src/main/resources/rest-api-spec/api[REST API specification]
  of {es}. This tool is available in `util/GenerateEndpoints.php`.
  https://github.com/elastic/elasticsearch-php/pull/966[#966]
* Fixed the asciidoc 
  https://www.elastic.co/guide/en/elasticsearch/client/php-api/current/ElasticsearchPHP_Endpoints.html[endpoints documentation] 
  based on the code generation using https://github.com/FriendsOfPHP/Sami[Sami] 
  project https://github.com/elastic/elasticsearch-php/pull/966[#966]
* All the `experimental` and `beta` APIs are now signed with a `@note` tag in 
  the phpdoc section (for example, 
  https://github.com/elastic/elasticsearch-php/blob/master/src/Elasticsearch/Client.php[$client->rankEval()]). 
  For more information read the 
  https://www.elastic.co/guide/en/elasticsearch/client/php-api/{branch}/experimental_and_beta_apis.html[experimental and beta APIs] 
  section in the documentation. 
  https://github.com/elastic/elasticsearch-php/pull/966[#966]
* Removed `AlreadyExpiredException` since it has been removed
  from {es} with https://github.com/elastic/elasticsearch/pull/24857[#24857]
  https://github.com/elastic/elasticsearch-php/pull/954[#954]


[discrete]
[[rn-7-3-0]]
=== 7.3.0

* Added support for simplified access to the `X-Opaque-Id` header
  https://github.com/elastic/elasticsearch-php/pull/952[#952]
* Added the HTTP port in the log messages
  https://github.com/elastic/elasticsearch-php/pull/950[#950]
* Fixed hostname with underscore (ClientBuilder::prependMissingScheme)
  https://github.com/elastic/elasticsearch-php/pull/949[#949]
* Removed unused Monolog in ClientBuilder
  https://github.com/elastic/elasticsearch-php/pull/948[#948]
  

[discrete]
[[rn-7-2-2]]
=== 7.2.2

* Reintroduced the optional parameter in 
  `Elasticsearch\Namespaces\IndicesNamespace::getAliases()`.
  This fixes the BC break introduced in 7.2.0 and 7.2.1.
  https://github.com/elastic/elasticsearch-php/pull/947[#947]


[discrete]
[[rn-7-2-1]]
=== 7.2.1

* Reintroduced `Elasticsearch\Namespaces\IndicesNamespace::getAliases()` as proxy
  to `IndicesNamespace::getAlias()` to prevent BC breaks. The `getAliases()` is
  marked as deprecated and it will be removed from `elasticsearch-php 8.0`
  https://github.com/elastic/elasticsearch-php/pull/943[#943]

[discrete]
==== Docs

* Fixed missing put mapping code snippet in code examples
  https://github.com/elastic/elasticsearch-php/pull/938[#938]


[discrete]
[[rn-7-2-0]]
=== 7.2.0

* Updated the API endpoints for working with {es} 7.2.0:
    * added `wait_for_active_shards` parameter to `indices.close` API;
    * added `expand_wildcards` parameter to `cluster.health` API;
    * added include_unloaded_segments`, `expand_wildcards`, `forbid_closed_indices`
      parameters to `indices.stats` API.
  https://github.com/elastic/elasticsearch-php/pull/933/commits/27d721ba44b8c199388650c5a1c8bd69757229aa[27d721b]
* Updated the phpdoc parameters for all the API endpoints
  https://github.com/elastic/elasticsearch-php/pull/933/commits/27d721ba44b8c199388650c5a1c8bd69757229aa[27d721b] 
* Improved the Travis CI speed using cache feature with composer
  https://github.com/elastic/elasticsearch-php/pull/929[#929]
* Fixed `php_uname()` usage checking if it is disabled
  https://github.com/elastic/elasticsearch-php/pull/927[#927]
* Added support of Elastic Cloud ID and API key authentication
  https://github.com/elastic/elasticsearch-php/pull/923[#923]


[discrete]
[[rn-7-1-1]]
=== 7.1.1

* Fixed `ClientBuilder::setSSLVerification()` to accept string or boolean
  https://github.com/elastic/elasticsearch-php/pull/917[#917]
* Fix type hinting for `setBody` in 
  `Elasticsearch\Endpoints\Ingest\Pipeline\Put`
  https://github.com/elastic/elasticsearch-php/pull/913[#913]


[discrete]
[[rn-7-1-0]]
=== 7.1.0

* Added warning log for {es} response containing the `Warning` header
  https://github.com/elastic/elasticsearch-php/pull/911[#911]
* Fixed #838 hosting company is blocking ports because of `YamlRunnerTest.php`
  https://github.com/elastic/elasticsearch-php/pull/844[#844]
* Specialized inheritance of `NoNodesAvailableException` to extend 
  `ServerErrorResponseException` instead of the generic `\Exception`
  https://github.com/elastic/elasticsearch-php/pull/607[#607]
* Fixed scroll TTL is extracted but not set as a body param
  https://github.com/elastic/elasticsearch-php/pull/907[#907]

[discrete]
==== Testing

* Improved the speed of integration tests removing snapshots delete from 
  `YamlRunnerTest::clean`
  https://github.com/elastic/elasticsearch-php/pull/911[#911]
* Reduced the number of skipping YAML integration tests from 20 to 6
  https://github.com/elastic/elasticsearch-php/pull/911[#911]

[discrete]
==== Docs

* Documentation updated for {es} 7
  https://github.com/elastic/elasticsearch-php/pull/904[#904]


[discrete]
[[rn-7-0-2]]
=== 7.0.2

* Fixed incorrect return type hint when using async requests/futures
  https://github.com/elastic/elasticsearch-php/pull/905[#905]


[discrete]
[[rn-7-0-1]]
=== 7.0.1

* Fixed SniffingConnectionPool removing the return type of Connection::sniff()
  https://github.com/elastic/elasticsearch-php/pull/899[#899]


[discrete]
[[rn-7-0-0]]
=== 7.0.0

* Requirement of PHP 7.1 instead of 7.0 that is not supported since 1 Jan 2019.
  https://github.com/elastic/elasticsearch-php/pull/897[#897]
* Code refactoring using type hints and return type declarations where possible
  https://github.com/elastic/elasticsearch-php/pull/897[#897]
* Update vendor libraries (PHPUnit 7.5, Symfony YAML 4.3, and so on)
  https://github.com/elastic/elasticsearch-php/pull/897[#897]
* Updated all the API endpoints using the 
  https://github.com/elastic/elasticsearch/tree/v7.0.0/rest-api-spec/src/main/resources/rest-api-spec/api[latest 7.0.0 specs] 
  of {es} https://github.com/elastic/elasticsearch-php/pull/897[#897]
* Added the `User-Agent` in each HTTP request 
  https://github.com/elastic/elasticsearch-php/pull/898[#898]
* Simplified the logging methods 
  `logRequestFail($request, $response, $exception)` and 
  `logRequestSuccess($request, $response)` in 
  `Elasticsearch\Connections\Connection`
  https://github.com/elastic/elasticsearch-php/pull/876[#876]
* Fix `json_encode` for unicode(emoji) characters 
  https://github.com/elastic/elasticsearch-php/pull/856[#856]
* Fix HTTP port specification using CURLOPT_PORT, not anymore in the host 
  https://github.com/elastic/elasticsearch-php/pull/782[#782]
