<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => 'cfa12054293f63d067814cd31c3b575692031a5f',
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => 'cfa12054293f63d067814cd31c3b575692031a5f',
            'dev_requirement' => false,
        ),
        'elastic/transport' => array(
            'pretty_version' => 'v8.8.0',
            'version' => '8.8.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../elastic/transport',
            'aliases' => array(),
            'reference' => 'cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b',
            'dev_requirement' => false,
        ),
        'elasticsearch/elasticsearch' => array(
            'pretty_version' => 'v8.14.0',
            'version' => '8.14.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../elasticsearch/elasticsearch',
            'aliases' => array(),
            'reference' => 'bff3c3e2402f6a20449404637f91a5ae214eff46',
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.10.1',
            'version' => '6.10.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'reference' => '500501c2ce893c824c801da135d02661199f60c5',
            'dev_requirement' => false,
        ),
        'google/apiclient' => array(
            'pretty_version' => 'v2.16.0',
            'version' => '2.16.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient',
            'aliases' => array(),
            'reference' => '017400f609c1fb71ab5ad824c50eabd4c3eaf779',
            'dev_requirement' => false,
        ),
        'google/apiclient-services' => array(
            'pretty_version' => 'v0.358.0',
            'version' => '0.358.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient-services',
            'aliases' => array(),
            'reference' => 'a6daf60ee25cb45b6e3dbd04b62d1df39a609fbd',
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.40.0',
            'version' => '1.40.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'reference' => 'bff9f2d01677e71a98394b5ac981b99523df5178',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.8.1',
            'version' => '7.8.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => '41042bc7ab002487b876a0683fc8dce04ddce104',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => 'bbff78d96034045e58e13dedd6ad91b5d1253223',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.6.2',
            'version' => '2.6.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => '45b30f99ac27b5ca93cb4831afe16285f57b8221',
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.6.0',
            'version' => '3.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'reference' => '4b18b21a5527a3d5ffdac2fd35d3ab25a9597654',
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.7.0',
            'version' => '2.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'reference' => '52a0d99e69f56b9ec27ace92ba56897fe6993105',
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/discovery' => array(
            'pretty_version' => '1.19.4',
            'version' => '1.19.4.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../php-http/discovery',
            'aliases' => array(),
            'reference' => '0700efda8d7526335132360167315fdab3aeb599',
            'dev_requirement' => false,
        ),
        'php-http/httplug' => array(
            'pretty_version' => '2.4.0',
            'version' => '2.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/httplug',
            'aliases' => array(),
            'reference' => '625ad742c360c8ac580fcc647a1541d29e257f67',
            'dev_requirement' => false,
        ),
        'php-http/promise' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/promise',
            'aliases' => array(),
            'reference' => 'fc85b1fba37c169a69a07ef0d5a8075770cc1f83',
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.37',
            'version' => '3.0.37.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'reference' => 'cfa2013d0f68c062055180dd4328cc8b9d1f30b8',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'fe5ea303b0887d5caefd3d431c3e61ad47037001',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'dev_requirement' => false,
        ),
    ),
);
