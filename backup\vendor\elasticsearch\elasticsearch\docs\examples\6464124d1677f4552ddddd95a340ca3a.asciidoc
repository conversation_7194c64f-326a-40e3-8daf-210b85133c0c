// api-conventions.asciidoc:353

[source, php]
----
$params = [
    'index' => 'library',
    'body' => [
        'title' => 'Book #1',
        'rating' => 200.1,
    ],
];
$response = $client->index($params);
$params = [
    'index' => 'library',
    'body' => [
        'title' => 'Book #2',
        'rating' => 1.7,
    ],
];
$response = $client->index($params);
$params = [
    'index' => 'library',
    'body' => [
        'title' => 'Book #3',
        'rating' => 0.1,
    ],
];
$response = $client->index($params);
$response = $client->search();
----
