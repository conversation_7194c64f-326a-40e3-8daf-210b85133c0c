// query-dsl/function-score-query.asciidoc:175

[source, php]
----
$params = [
    'body' => [
        'query' => [
            'function_score' => [
                'query' => [
                    'match' => [
                        'message' => 'elasticsearch',
                    ],
                ],
                'script_score' => [
                    'script' => [
                        'params' => [
                            'a' => 5,
                            'b' => 1.2,
                        ],
                        'source' => 'params.a / Math.pow(params.b, doc[\'likes\'].value)',
                    ],
                ],
            ],
        ],
    ],
];
$response = $client->search($params);
----
