// search/request/scroll.asciidoc:206

[source, php]
----
$params = [
    'index' => 'twitter',
    'body' => [
        'slice' => [
            'id' => 0,
            'max' => 2,
        ],
        'query' => [
            'match' => [
                'title' => 'elasticsearch',
            ],
        ],
    ],
];
$response = $client->search($params);
$params = [
    'index' => 'twitter',
    'body' => [
        'slice' => [
            'id' => 1,
            'max' => 2,
        ],
        'query' => [
            'match' => [
                'title' => 'elasticsearch',
            ],
        ],
    ],
];
$response = $client->search($params);
----
