// search/request/sort.asciidoc:289

[source, php]
----
$params = [
    'body' => [
        'query' => [
            'nested' => [
                'path' => 'parent',
                'query' => [
                    'bool' => [
                        'must' => [
                            'range' => [
                                'parent.age' => [
                                    'gte' => 21,
                                ],
                            ],
                        ],
                        'filter' => [
                            'nested' => [
                                'path' => 'parent.child',
                                'query' => [
                                    'match' => [
                                        'parent.child.name' => 'matt',
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
        'sort' => [
            [
                'parent.child.age' => [
                    'mode' => 'min',
                    'order' => 'asc',
                    'nested' => [
                        'path' => 'parent',
                        'filter' => [
                            'range' => [
                                'parent.age' => [
                                    'gte' => 21,
                                ],
                            ],
                        ],
                        'nested' => [
                            'path' => 'parent.child',
                            'filter' => [
                                'match' => [
                                    'parent.child.name' => 'matt',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
];
$response = $client->search($params);
----
