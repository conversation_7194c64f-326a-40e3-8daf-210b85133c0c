<?php

session_start();

if (!isset($_SESSION['user_email'])) {
    header('Location: login.php');
    exit;
}

header("Cache-Control: no-cache, must-revalidate");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT");
header("Pragma: no-cache");

if ($_SESSION['user_role'] !== 'admin') {
    echo 'Access denied. You do not have permission to access this page.';
    exit;
}

// MongoDB 連接設置
require_once 'vendor/autoload.php';
require_once 'vendor/vlucas/phpdotenv/src/Dotenv.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$mongoHost = $_ENV['MONGO_HOST'];
$mongoPort = $_ENV['MONGO_PORT'];
$mongoUser = $_ENV['MONGO_USER'];
$mongoPass = $_ENV['MONGO_PASS'];
$mongoDb = $_ENV['MONGO_DB_USER']; // 使用 optools 數據庫
$mongoAuth = $_ENV['MONGO_AUTH_DB'];

$mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";

$mongodb_connected = false;
$mongodb_error = '';
$client = null;
$collection = null;

try {
    $client = new MongoDB\Client($mongoUri);
    $client->listDatabases(); // 測試連接
    $collection = $client->$mongoDb->users; // 使用 'users' 集合
    $mongodb_connected = true;
    error_log("MongoDB 連接成功 (users_permissed.php)");
} catch (Exception $e) {
    $mongodb_error = 'MongoDB connection failed: ' . $e->getMessage();
    error_log($mongodb_error);
}

///////////////////////////////////////////////

// 處理 GET 請求 - 返回用戶數據
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_users') {
    header('Content-Type: application/json');

    try {
        if (!$mongodb_connected) {
            throw new Exception('MongoDB connection required');
        }

        // 從 MongoDB 讀取所有用戶數據
        $cursor = $collection->find();
        $users_data = [];

        foreach ($cursor as $doc) {
            $user = [];
            foreach ($doc as $key => $value) {
                if ($value instanceof MongoDB\BSON\ObjectId) {
                    $user[$key] = (string) $value;
                } elseif ($value instanceof MongoDB\BSON\UTCDateTime) {
                    $user[$key] = $value->toDateTime()->format('Y-m-d H:i:s');
                } else {
                    $user[$key] = $value;
                }
            }

            // 確保 function 字段存在
            if (!isset($user['function'])) {
                $user['function'] = [];
            }

            $users_data[] = $user;
        }

        echo json_encode($users_data);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    try {
        // 檢查 MongoDB 連接
        if (!$mongodb_connected) {
            throw new Exception('MongoDB connection required for data operations');
        }

        // 獲取輸入數據
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if ($data === null) {
            throw new Exception('Invalid JSON data');
        }

        // 驗證數據結構
        if (!is_array($data)) {
            throw new Exception('Data must be an array');
        }

        // 更新 MongoDB 中的用戶數據
        $updated_count = 0;
        foreach ($data as $user_data) {
            if (!isset($user_data['email'])) {
                throw new Exception('User data missing email field');
            }

            $email = $user_data['email'];

            // 更新用戶的 function 字段
            $result = $collection->updateOne(
                ['email' => $email],
                ['$set' => ['function' => $user_data['function'] ?? []]]
            );

            if ($result->getModifiedCount() > 0 || $result->getMatchedCount() > 0) {
                $updated_count++;
            }
        }

        // 驗證更新是否成功
        if ($updated_count === 0) {
            throw new Exception('No users were updated');
        }

        echo json_encode([
            'success' => true,
            'updated_count' => $updated_count
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Function Manager</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash/4.17.21/lodash.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        /* 在原有的 CSS 中添加 */
        .edit-icon {
            color: #2196F3;
            cursor: pointer;
            font-size: 18px;
            margin-left: 10px;
        }

        .function-name {
            padding: 2px 5px;
            border-radius: 3px;
        }

        .function-name.editing {
            background: white;
            border: 1px solid #ddd;
            outline: none;
            min-width: 100px;
        }


        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            min-width: 300px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .modal-buttons {
            text-align: right;
            margin-top: 20px;
        }

        /* 新增與刪除的圖示大小修改 */
        .add-icon {
            color: #4CAF50;
            cursor: pointer;
            font-size: 36px;
            font-weight: bold;
            margin-left: 10px;
        }

        .delete-icon {
            color: #f44336;
            cursor: pointer;
            font-size: 44px;
            float: right;
            margin-left: 10px;
        }

        .function-cell {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }



        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .function-table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;
        }

        .function-table th,
        .function-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        /* 修改第一列的寬度 */
        .function-table th:first-child,
        .function-table td:first-child {
            min-width: 300px;
            /* 設定最小寬度 */
            width: 300px;
            /* 設定固定寬度 */
        }


        .function-table th {
            background-color: #f4f4f4;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .parent-category {
            font-weight: bold;
            background-color: #f8f8f8;
            cursor: pointer;
        }

        /* 修改父類別和子類別中的數字計數的對齊方式 */
        .function-table td:not(:first-child),
        .function-table th:not(:first-child) {
            text-align: center;
            /* 除了第一列外，其他列置中對齊 */
        }

        /* 調整勾選框的容器對齊方式 */
        .function-table td:not(:first-child) {
            text-align: center;
            vertical-align: middle;
        }



        /* 調整父類別中的計數數字的對齊方式 */
        .parent-category td:not(:first-child) {
            text-align: center;
        }

        .parent-category td:first-child {
            position: relative;
            padding-left: 25px;
        }

        .parent-category td:first-child:before {
            content: '▼';
            position: absolute;
            left: 8px;
            transition: transform 0.3s;
        }

        .parent-category.collapsed td:first-child:before {
            transform: rotate(-90deg);
        }

        .child-function {
            padding-left: 20px;
            transition: all 0.3s ease;
        }

        .child-function.hidden {
            display: none;
        }

        /* 將這些樣式替換掉原來的 button-container 相關樣式 */
        .button-container {
            margin-bottom: 20px;
            text-align: right;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .function-table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 10px;
            /* 減少上方間距 */
        }

        .button {
            padding: 8px 16px;
            margin-left: 10px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }

        .save-button {
            background-color: #4CAF50;
            color: white;
        }

        .cancel-button {
            background-color: #f44336;
            color: white;
        }

        .toggle-all-button {
            background-color: #2196F3;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container">

        <!-- 在原有的 Modal 區塊中新增 -->
        <!-- 編輯功能的彈窗 -->
        <div id="editFunctionModal" class="modal">
            <div class="modal-content">
                <h2>編輯功能</h2>
                <div class="form-group">
                    <label for="editCategorySelect">類別：</label>
                    <select id="editCategorySelect" required>
                        <!-- 選項將由 JavaScript 動態填充 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="editFunctionName">功能名稱：</label>
                    <input type="text" id="editFunctionName" required>
                </div>
                <div class="form-group">
                    <label for="editFunctionUrl">對應網址：</label>
                    <input type="text" id="editFunctionUrl" required>
                </div>
                <div class="modal-buttons">
                    <button class="button save-button" onclick="saveEditFunction()">確定</button>
                    <button class="button cancel-button" onclick="closeEditModal()">取消</button>
                </div>
            </div>
        </div>

        <!-- 新增功能的彈窗 -->
        <div id="addFunctionModal" class="modal">
            <div class="modal-content">
                <h2>新增功能</h2>
                <div class="form-group">
                    <label for="categorySelect">選擇類別：</label>
                    <select id="categorySelect" required>
                        <!-- 選項將由 JavaScript 動態填充 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="functionName">功能名稱：</label>
                    <input type="text" id="functionName" required>
                </div>
                <div class="form-group">
                    <label for="functionUrl">對應網址：</label>
                    <input type="text" id="functionUrl" required>
                </div>
                <div class="modal-buttons">
                    <button class="button save-button" onclick="saveNewFunction()">確定</button>
                    <button class="button cancel-button" onclick="closeAddModal()">取消</button>
                </div>
            </div>
        </div>

        <!-- 刪除確認的彈窗 -->
        <div id="deleteConfirmModal" class="modal">
            <div class="modal-content">
                <h2>確認刪除</h2>
                <p>確定要刪除此功能嗎？</p>
                <div class="modal-buttons">
                    <button class="button save-button" onclick="confirmDelete()">確定</button>
                    <button class="button cancel-button" onclick="closeDeleteModal()">取消</button>
                </div>
            </div>
        </div>

        <div class="button-container">
            <button class="button toggle-all-button" onclick="toggleAllCategories()">展開/摺疊全部</button>
            <button class="button save-button" onclick="saveChanges()">確定</button>
            <button class="button cancel-button" onclick="cancelChanges()">取消</button>
        </div>
        <table id="functionTable" class="function-table">
            <!-- Table will be populated by JavaScript -->
        </table>
    </div>

    <script>
        let userData = [];
        let originalData = [];
        /////////////////////////
        ////////////////////////////////////////////////////

        let allExpanded = false;

        //存取網頁時若要預設摺疊與展開，請修改以下三處:
        // // 將這行從
        // let allExpanded = true;
        // // 改為
        // let allExpanded = false;
        // // 在 renderTable 函數中，將這行
        // categoryRow.className = 'parent-category collapsed';
        // // 改為
        // categoryRow.className = 'parent-category';
        // // 在 renderTable 函數中，將這行
        // functionRow.className = 'child-function hidden';
        // // 改為
        // functionRow.className = 'child-function';

        // 當前要刪除的功能信息
        let currentDeletingFunction = null;


        // 添加以下新函數
        let currentEditingElement = null;
        let originalName = '';


        // 當前正在編輯的功能信息
        let currentEditingFunction = null;

        // 顯示編輯模態框
        function showEditModal(category, funcName) {
            event.stopPropagation();
            const modal = document.getElementById('editFunctionModal');
            const categorySelect = document.getElementById('editCategorySelect');
            const functionNameInput = document.getElementById('editFunctionName');
            const functionUrlInput = document.getElementById('editFunctionUrl');

            // 清空現有選項
            categorySelect.innerHTML = '';

            // 獲取並添加所有類別選項
            const categories = getAllCategories();
            categories.forEach(cat => {
                const option = document.createElement('option');
                option.value = cat;
                option.textContent = cat;
                option.selected = cat === category;
                categorySelect.appendChild(option);
            });

            // 獲取當前功能的 URL
            // 修改這部分來確保能找到正確的 URL
            let functionUrl = '';
            for (let user of userData) {
                if (user.function &&
                    user.function[category] &&
                    user.function[category][funcName] &&
                    user.function[category][funcName][0]) {
                    functionUrl = user.function[category][funcName][0];
                    break;
                }
            }


            // 設置當前值
            functionNameInput.value = funcName;
            functionUrlInput.value = functionUrl;

            // 保存當前正在編輯的功能信息
            currentEditingFunction = {
                originalCategory: category,
                originalName: funcName
            };

            modal.style.display = 'flex';
        }

        // 關閉編輯模態框
        function closeEditModal() {
            document.getElementById('editFunctionModal').style.display = 'none';
            currentEditingFunction = null;
        }

        // 修改 saveEditFunction 函數
        async function saveEditFunction() {
            const newCategory = document.getElementById('editCategorySelect').value.trim();
            const newName = document.getElementById('editFunctionName').value.trim();
            const newUrl = document.getElementById('editFunctionUrl').value.trim();

            if (!newCategory || !newName || !newUrl) {
                alert('請填寫所有欄位');
                return;
            }

            try {
                const {
                    originalCategory,
                    originalName
                } = currentEditingFunction;

                // 檢查原始資料是否存在
                if (!originalCategory || !originalName) {
                    throw new Error('無法找到原始資料');
                }

                // 更新所有用戶的功能
                userData.forEach(user => {
                    // 確保 function 對象存在
                    if (!user.function) {
                        user.function = {};
                    }

                    // 確保原始類別存在
                    if (!user.function[originalCategory]) {
                        user.function[originalCategory] = {};
                    }

                    // 確保目標類別存在
                    if (!user.function[newCategory]) {
                        user.function[newCategory] = {};
                    }

                    // 獲取原始功能的狀態（如果存在）
                    const originalFunction = user.function[originalCategory][originalName];
                    const isEnabled = originalFunction ? originalFunction[1] : false;

                    // 如果改變了類別或名稱
                    if (newCategory !== originalCategory || newName !== originalName) {
                        // 在新位置創建功能
                        user.function[newCategory][newName] = [newUrl, isEnabled];

                        // 如果原始功能存在，則刪除它
                        if (user.function[originalCategory][originalName]) {
                            delete user.function[originalCategory][originalName];
                        }
                    } else {
                        // 如果只改變了 URL
                        // 確保功能數組存在且格式正確
                        if (!Array.isArray(user.function[originalCategory][originalName])) {
                            user.function[originalCategory][originalName] = [newUrl, isEnabled];
                        } else {
                            user.function[originalCategory][originalName][0] = newUrl;
                        }
                    }

                    // 清理空的類別
                    for (const category in user.function) {
                        if (Object.keys(user.function[category]).length === 0) {
                            delete user.function[category];
                        }
                    }
                });

                // 保存到服務器
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    closeEditModal();
                    alert('功能已更新');
                    window.location.reload();
                } else {
                    throw new Error(result.error || 'Save failed');
                }
            } catch (error) {
                console.error('Error updating function:', error);
                alert('更新失敗：' + error.message);
            }
        }

        ///////////////////////////////////////////

        function startEditing(event, element, category, oldName) {
            event.stopPropagation();

            // 如果已經在編輯中，則不執行
            if (element.classList.contains('editing')) {
                return;
            }

            // 保存當前編輯的元素和原始名稱
            currentEditingElement = element;
            originalName = oldName;

            // 使元素可編輯
            element.contentEditable = true;
            element.classList.add('editing');
            element.focus();

            // 選中所有文字
            const range = document.createRange();
            range.selectNodeContents(element);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // 添加事件監聽器
            document.addEventListener('click', handleClickOutside);
            element.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    finishEditing(category, oldName);
                }
            });
        }

        function handleClickOutside(event) {
            if (currentEditingElement && !currentEditingElement.contains(event.target)) {
                const category = currentEditingElement.closest('tr').querySelector('[data-category]').dataset.category;
                finishEditing(category, originalName);
            }
        }

        async function finishEditing(category, oldName) {
            if (!currentEditingElement) return;

            const newName = currentEditingElement.textContent.trim();

            // 如果名稱沒有改變，直接結束編輯
            if (newName === originalName) {
                exitEditMode();
                return;
            }

            // 檢查新名稱是否為空
            if (!newName) {
                alert('功能名稱不能為空');
                currentEditingElement.textContent = originalName;
                exitEditMode();
                return;
            }

            try {
                // 更新所有用戶的功能名稱
                userData.forEach(user => {
                    if (user.function?.[category]?.[oldName]) {
                        // 保存原始值
                        const originalValue = user.function[category][oldName];
                        // 刪除舊的鍵
                        delete user.function[category][oldName];
                        // 添加新的鍵
                        user.function[category][newName] = originalValue;
                    }
                });

                // 保存到服務器
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                if (response.ok) {
                    // 重新載入頁面以顯示更新
                    window.location.reload();
                } else {
                    throw new Error('Update failed');
                }
            } catch (error) {
                console.error('Error updating function name:', error);
                alert('更新失敗：' + error.message);
                currentEditingElement.textContent = originalName;
            }

            exitEditMode();
        }

        function exitEditMode() {
            if (currentEditingElement) {
                currentEditingElement.contentEditable = false;
                currentEditingElement.classList.remove('editing');
                document.removeEventListener('click', handleClickOutside);
                currentEditingElement = null;
                originalName = '';
            }
        }

        // 獲取所有父類別
        function getAllCategories() {
            const categories = new Set();
            userData.forEach(user => {
                if (user.function) {
                    Object.keys(user.function).forEach(category => {
                        categories.add(category);
                    });
                }
            });
            return Array.from(categories).sort();
        }

        // 顯示新增功能彈窗
        function showAddModal() {
            const modal = document.getElementById('addFunctionModal');
            const categorySelect = document.getElementById('categorySelect');

            // 清空現有選項
            categorySelect.innerHTML = '';

            // 添加預設選項
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '請選擇類別';
            categorySelect.appendChild(defaultOption);

            // 獲取並添加所有類別選項
            const categories = getAllCategories();
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categorySelect.appendChild(option);
            });

            // 重置其他輸入欄位
            document.getElementById('functionName').value = '';
            document.getElementById('functionUrl').value = '';

            modal.style.display = 'flex';
        }

        // 關閉新增功能彈窗
        function closeAddModal() {
            document.getElementById('addFunctionModal').style.display = 'none';
        }


        // 保存新功能
        async function saveNewFunction() {
            const category = document.getElementById('categorySelect').value.trim();
            const name = document.getElementById('functionName').value.trim();
            const url = document.getElementById('functionUrl').value.trim();

            if (!category || !name || !url) {
                alert('請填寫所有欄位');
                return;
            }

            try {
                // 將新功能添加到每個用戶
                userData.forEach(user => {
                    // 確保 function 對象存在
                    if (!user.function) {
                        user.function = {};
                    }

                    // 確保類別是一個對象而不是數組
                    if (!user.function[category] || Array.isArray(user.function[category])) {
                        user.function[category] = {};
                    }

                    // 添加新功能
                    user.function[category][name] = [url, false];
                });

                // 保存到服務器
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    closeAddModal();
                    alert('功能新增成功');
                    window.location.reload();
                } else {
                    throw new Error(result.error || 'Save failed');
                }
            } catch (error) {
                console.error('Error saving new function:', error);
                alert('保存失敗：' + error.message);
            }
        }

        // 顯示刪除確認彈窗
        function showDeleteModal(category, func) {
            event.stopPropagation();
            currentDeletingFunction = {
                category,
                func
            };
            document.getElementById('deleteConfirmModal').style.display = 'flex';
        }

        // 關閉刪除確認彈窗
        function closeDeleteModal() {
            document.getElementById('deleteConfirmModal').style.display = 'none';
            currentDeletingFunction = null;
        }

        // 確認刪除功能
        async function confirmDelete() {
            if (!currentDeletingFunction) return;

            try {
                const {
                    category,
                    func
                } = currentDeletingFunction;

                // 從每個用戶中刪除該功能
                userData.forEach(user => {
                    if (user.function?.[category]?.[func]) {
                        delete user.function[category][func];
                    }
                });

                // 保存到服務器
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                if (response.ok) {
                    closeDeleteModal();
                    alert('功能已刪除');
                    window.location.reload();
                } else {
                    throw new Error('Delete failed');
                }
            } catch (error) {
                console.error('Error deleting function:', error);
                alert('刪除失敗：' + error.message);
            }
        }

        // 載入用戶數據
        async function loadUserData() {
            try {
                // 從 MongoDB 讀取用戶數據
                const response = await fetch(window.location.href + '?action=get_users&t=' + new Date().getTime());

                if (!response.ok) {
                    throw new Error('Failed to fetch user data from server');
                }

                userData = await response.json();

                if (!validateUserData(userData)) {
                    throw new Error('Invalid data structure from MongoDB');
                }

                originalData = JSON.parse(JSON.stringify(userData));
                renderTable();
            } catch (error) {
                console.error('Error loading user data:', error);
                alert('載入數據失敗：' + error.message);
            }
        }

        // ! 表格的父項目的排序(由上到下)
        // 此方法（父分類的優先級排序，由上到下）：
        // 更靈活
        // 容易添加新的類別
        // 可以處理未定義的類別
        // 修改 getAllFunctions 函數
        function getAllFunctions() {
            const allFunctions = new Map();

            // 定義類別的排序優先級
            const categoryPriority = {
                'MACROSS': 1,
                'THOR': 2,
                'LEO': 3,
                '後台管理': 4
            };

            // 先收集所有功能
            userData.forEach(user => {
                if (user.function) {
                    Object.entries(user.function).forEach(([category, functions]) => {
                        if (!allFunctions.has(category)) {
                            allFunctions.set(category, new Set());
                        }
                        Object.keys(functions).forEach(func => {
                            allFunctions.get(category).add(func);
                        });
                    });
                }
            });

            // 將類別轉換為數組並排序
            const sortedCategories = Array.from(allFunctions.keys()).sort((a, b) => {
                const priorityA = categoryPriority[a] || 999; // 未定義優先級的放到最後
                const priorityB = categoryPriority[b] || 999;
                return priorityA - priorityB;
            });

            // 創建新的有序 Map
            const sortedFunctions = new Map();
            sortedCategories.forEach(category => {
                sortedFunctions.set(category, allFunctions.get(category));
            });

            return sortedFunctions;
        }

        // 切換類別的展開/摺疊
        function toggleCategory(categoryRow) {
            const isCollapsed = categoryRow.classList.toggle('collapsed');
            let row = categoryRow.nextElementSibling;

            while (row && !row.classList.contains('parent-category')) {
                if (isCollapsed) {
                    row.classList.add('hidden');
                } else {
                    row.classList.remove('hidden');
                }
                row = row.nextElementSibling;
            }
        }

        // 切換全部類別的展開/摺疊
        function toggleAllCategories() {
            const parentCategories = document.querySelectorAll('.parent-category');
            allExpanded = !allExpanded;

            parentCategories.forEach(category => {
                const isCurrentlyCollapsed = category.classList.contains('collapsed');
                if (allExpanded && isCurrentlyCollapsed) {
                    toggleCategory(category);
                } else if (!allExpanded && !isCurrentlyCollapsed) {
                    toggleCategory(category);
                }
            });
        }

        // 修改 renderTable 函數中處理父類別的部分
        function renderTable() {
            const table = document.getElementById('functionTable');
            table.innerHTML = '';

            // 創建表頭
            const headerRow = document.createElement('tr');
            headerRow.innerHTML = '<th>功能<span class="add-icon" onclick="showAddModal()">+</span></th>';
            userData.forEach(user => {
                headerRow.innerHTML += `<th>${user.email}</th>`;
            });
            table.appendChild(headerRow);

            // 獲取所有功能
            const allFunctions = getAllFunctions();

            // 添加功能行
            allFunctions.forEach((functions, category) => {
                // 添加父類別行
                const categoryRow = document.createElement('tr');

                //categoryRow.className = 'parent-category collapsed';
                categoryRow.className = 'parent-category';

                // 計算每個用戶在此類別下已勾選的子項目數量
                const cells = [`<td>${category}</td>`];
                userData.forEach((user, userIndex) => {
                    const checkedCount = Array.from(functions).reduce((count, func) => {
                        const isChecked = user.function?.[category]?.[func]?.[1] ?? false;
                        return count + (isChecked ? 1 : 0);
                    }, 0);

                    const totalFunctions = functions.size;
                    cells.push(`<td>${checkedCount > 0 ? `(${checkedCount})` : ''}</td>`);
                });

                categoryRow.innerHTML = cells.join('');
                categoryRow.addEventListener('click', () => toggleCategory(categoryRow));
                table.appendChild(categoryRow);

                // 在 renderTable 函數中找到 functions.forEach 部分，修改為：
                functions.forEach(func => {
                    const functionRow = document.createElement('tr');

                    //functionRow.className = 'child-function hidden';
                    functionRow.className = 'child-function';

                    const cells = [`<td><div class="function-cell">
                        <span class="function-name">${func}</span>
                        <div>
                            <span class="edit-icon" onclick="showEditModal('${category}', '${func}')">✎</span>
                            <span class="delete-icon" onclick="showDeleteModal('${category}', '${func}')">-</span>
                        </div>
                    </div></td>`];

                    userData.forEach((user, userIndex) => {
                        const isChecked = user.function?.[category]?.[func]?.[1] ?? false;
                        cells.push(`
                            <td>
                                <input type="checkbox" 
                                    ${isChecked ? 'checked' : ''} 
                                    onchange="updateFunction('${category}', '${func}', ${userIndex}, this.checked)"
                                    data-category="${category}"
                                    data-function="${func}"
                                    data-user-index="${userIndex}"
                                />
                            </td>
                        `);
                    });

                    functionRow.innerHTML = cells.join('');
                    table.appendChild(functionRow);
                });

            });
        }

        async function saveChanges() {
            try {
                // 深度複製當前數據
                const dataToSave = JSON.parse(JSON.stringify(userData));

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(dataToSave)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    alert('更改已保存');
                    // 強制重新載入，不使用緩存
                    window.location.href = window.location.href + '?t=' + new Date().getTime();
                } else {
                    throw new Error(result.error || 'Save failed');
                }
            } catch (error) {
                console.error('Error saving changes:', error);
                alert('保存失敗：' + error.message);
            }
        }

        // 添加這個輔助函數來驗證數據
        function validateUserData(data) {
            try {
                // 檢查是否為陣列
                if (!Array.isArray(data)) {
                    throw new Error('Data must be an array');
                }

                // 檢查每個用戶對象
                data.forEach((user, index) => {
                    if (!user.email) {
                        throw new Error(`User at index ${index} missing email`);
                    }

                    if (user.function) {
                        // 檢查 function 對象的結構
                        Object.entries(user.function).forEach(([category, functions]) => {
                            if (typeof functions !== 'object') {
                                throw new Error(`Invalid functions for category ${category}`);
                            }
                        });
                    }
                });

                return true;
            } catch (error) {
                console.error('Data validation error:', error);
                return false;
            }
        }

        // 修改 updateFunction 函數
        function updateFunction(category, func, userIndex, checked) {
            try {
                // 深度複製當前數據
                let updatedUserData = JSON.parse(JSON.stringify(userData));

                // 確保必要的對象結構存在
                if (!updatedUserData[userIndex].function) {
                    updatedUserData[userIndex].function = {};
                }
                if (!updatedUserData[userIndex].function[category]) {
                    updatedUserData[userIndex].function[category] = {};
                }

                // 如果功能不存在，創建它
                if (!updatedUserData[userIndex].function[category][func]) {
                    // 從其他用戶複製URL
                    const sourceUser = updatedUserData.find(u => u.function?.[category]?.[func]);
                    const url = sourceUser ? sourceUser.function[category][func][0] : '';
                    updatedUserData[userIndex].function[category][func] = [url, checked];
                } else {
                    // 更新現有功能的狀態
                    updatedUserData[userIndex].function[category][func][1] = checked;
                }

                // 驗證更新後的數據
                if (!validateUserData(updatedUserData)) {
                    throw new Error('Invalid data structure after update');
                }

                // 更新全局數據
                userData = updatedUserData;

                // 更新父類別的數字顯示
                updateParentCategoryCount(category, userIndex);

            } catch (error) {
                console.error('Error updating function:', error);
                alert('更新失敗：' + error.message);
            }
        }

        // 添加更新父類別計數的輔助函數
        function updateParentCategoryCount(category, userIndex) {
            const functions = getAllFunctions().get(category);
            if (functions) {
                const checkedCount = Array.from(functions).reduce((count, f) => {
                    return count + (userData[userIndex].function?.[category]?.[f]?.[1] ? 1 : 0);
                }, 0);

                const parentRow = Array.from(document.querySelectorAll('.parent-category'))
                    .find(row => row.cells[0].textContent === category);

                if (parentRow) {
                    parentRow.cells[userIndex + 1].textContent = checkedCount > 0 ? `(${checkedCount})` : '';
                }
            }
        }

        // 取消更改
        function cancelChanges() {
            userData = JSON.parse(JSON.stringify(originalData));
            renderTable();
        }

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', loadUserData);
    </script>
</body>

</html>
