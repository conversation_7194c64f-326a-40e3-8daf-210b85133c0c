// query-dsl/function-score-query.asciidoc:41

[source, php]
----
$params = [
    'body' => [
        'query' => [
            'function_score' => [
                'query' => [
                    'match_all' => [
                    ],
                ],
                'boost' => '5',
                'functions' => [
                    [
                        'filter' => [
                            'match' => [
                                'test' => 'bar',
                            ],
                        ],
                        'random_score' => [
                        ],
                        'weight' => 23,
                    ],
                    [
                        'filter' => [
                            'match' => [
                                'test' => 'cat',
                            ],
                        ],
                        'weight' => 42,
                    ],
                ],
                'max_boost' => 42,
                'score_mode' => 'max',
                'boost_mode' => 'multiply',
                'min_score' => 42,
            ],
        ],
    ],
];
$response = $client->search($params);
----
