// search/request/sort.asciidoc:568

[source, php]
----
$params = [
    'body' => [
        'query' => [
            'term' => [
                'user' => 'kimchy',
            ],
        ],
        'sort' => [
            '_script' => [
                'type' => 'number',
                'script' => [
                    'lang' => 'painless',
                    'source' => 'doc[\'field_name\'].value * params.factor',
                    'params' => [
                        'factor' => 1.1,
                    ],
                ],
                'order' => 'asc',
            ],
        ],
    ],
];
$response = $client->search($params);
----
