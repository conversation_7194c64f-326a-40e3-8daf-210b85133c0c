<?php
/**
 * 用戶數據遷移腳本：從 JSON 文件遷移到 MongoDB
 *
 * 使用方法：
 * php migrate_users_to_mongodb.php
 */

require 'vendor/autoload.php';

// 載入 .env - 參考 access_records.php 的方式
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$mongoUser = $_ENV['MONGO_USER'];
$mongoPass = $_ENV['MONGO_PASS'];
$mongoHost = $_ENV['MONGO_HOST'];
$mongoPort = $_ENV['MONGO_PORT'];
$mongoDb   = $_ENV['MONGO_DB'];
$mongoAuth = $_ENV['MONGO_AUTH_DB'];

$mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";

echo "開始用戶數據遷移...\n";
echo "MongoDB URI: {$mongoUri}\n";

try {
    // 連接 MongoDB - 參考 access_records.php 的方式
    $client = new MongoDB\Client($mongoUri);
    // 測試連接
    $client->listDatabases();
    $collection = $client->$mongoDb->users;

    echo "MongoDB 連接成功\n";
    
    // 讀取 JSON 文件
    $jsonFile = 'users.json';
    if (!file_exists($jsonFile)) {
        throw new Exception("JSON 文件不存在: {$jsonFile}");
    }
    
    $jsonData = file_get_contents($jsonFile);
    $users = json_decode($jsonData, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("JSON 解析錯誤: " . json_last_error_msg());
    }
    
    echo "成功讀取 JSON 文件，共 " . count($users) . " 個用戶\n";
    
    // 檢查集合是否已存在數據
    $existingCount = $collection->countDocuments();
    if ($existingCount > 0) {
        echo "警告：MongoDB 集合中已存在 {$existingCount} 個文檔\n";
        echo "是否要清空現有數據並重新導入？(y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) === 'y') {
            $result = $collection->deleteMany([]);
            echo "已刪除 " . $result->getDeletedCount() . " 個現有文檔\n";
        } else {
            echo "取消遷移\n";
            exit(1);
        }
    }
    
    // 批量插入用戶數據
    $insertedCount = 0;
    $errors = [];
    
    foreach ($users as $index => $user) {
        try {
            // 確保必要字段存在
            if (!isset($user['email']) || empty($user['email'])) {
                $errors[] = "用戶 #{$index}: 缺少 email 字段";
                continue;
            }
            
            // 檢查是否已存在相同 email 的用戶
            $existingUser = $collection->findOne(['email' => $user['email']]);
            if ($existingUser) {
                echo "跳過重複用戶: " . $user['email'] . "\n";
                continue;
            }
            
            // 插入用戶
            $result = $collection->insertOne($user);
            if ($result->getInsertedId()) {
                $insertedCount++;
                echo "已插入用戶: " . $user['email'] . "\n";
            }
            
        } catch (Exception $e) {
            $errors[] = "用戶 " . ($user['email'] ?? "#{$index}") . ": " . $e->getMessage();
        }
    }
    
    echo "\n=== 遷移完成 ===\n";
    echo "成功插入: {$insertedCount} 個用戶\n";
    echo "錯誤數量: " . count($errors) . "\n";
    
    if (!empty($errors)) {
        echo "\n錯誤詳情:\n";
        foreach ($errors as $error) {
            echo "- {$error}\n";
        }
    }
    
    // 創建索引以提高查詢性能
    echo "\n創建索引...\n";
    
    // 為 email 字段創建唯一索引
    $collection->createIndex(['email' => 1], ['unique' => true]);
    echo "已創建 email 唯一索引\n";
    
    // 為 status 字段創建索引
    $collection->createIndex(['status' => 1]);
    echo "已創建 status 索引\n";
    
    // 為 role 字段創建索引
    $collection->createIndex(['role' => 1]);
    echo "已創建 role 索引\n";
    
    // 驗證遷移結果
    $finalCount = $collection->countDocuments();
    echo "\n最終 MongoDB 中的用戶數量: {$finalCount}\n";
    
    // 顯示一些統計信息
    $activeUsers = $collection->countDocuments(['status' => 'active']);
    $lockedUsers = $collection->countDocuments(['status' => 'locked']);
    $adminUsers = $collection->countDocuments(['role' => 'admin']);
    $regularUsers = $collection->countDocuments(['role' => 'user']);
    
    echo "\n用戶統計:\n";
    echo "- 活躍用戶: {$activeUsers}\n";
    echo "- 鎖定用戶: {$lockedUsers}\n";
    echo "- 管理員: {$adminUsers}\n";
    echo "- 普通用戶: {$regularUsers}\n";
    
    echo "\n遷移成功完成！\n";
    
} catch (Exception $e) {
    echo "遷移失敗: " . $e->getMessage() . "\n";
    echo "堆棧跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
