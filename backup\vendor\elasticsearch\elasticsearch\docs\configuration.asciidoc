[[configuration]]
== Configuration

Almost every aspect of the client is configurable. Most users only need to 
configure a few parameters to suit their needs, but it is possible to completely 
replace much of the internals if required.

Custom configuration is accomplished before the client is instantiated, through 
the `ClientBuilder` class. You can find all the configuration options and 
check sample code that helps you replace the various components.

To learn more about JSON in PHP, read <<php_json_objects>>.

* <<host-config>>
* <<set-retries>>
* <<http-meta-data>>
* <<enabling_logger>>
* <<http-client>>
* <<namespaces>>
* <<node_pool>>
* <<config-hash>>

include::php_json_objects.asciidoc[]

include::host-config.asciidoc[]

include::set-retries.asciidoc[]

include::http-meta-data.asciidoc[]

include::logger.asciidoc[]

include::http-client.asciidoc[]

include::namespaces.asciidoc[]

include::node-pool.asciidoc[]

include::config-hash.asciidoc[]
