<?php
session_start();

if (!isset($_SESSION['user_email'])) {
    header('Location: ../login.html');
    exit;
}

header("Cache-Control: no-cache, must-revalidate");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT");
header("Pragma: no-cache");

if ($_SESSION['user_role'] !== 'admin') {
    echo 'Access denied. You do not have permission to access this page.';
    exit;
}

// MongoDB 配置 - 參考 access_records.php 的連接方式
require '../vendor/autoload.php'; // Composer 自動載入

// 載入 .env
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

$mongoUser = $_ENV['MONGO_USER'];
$mongoPass = $_ENV['MONGO_PASS'];
$mongoHost = $_ENV['MONGO_HOST'];
$mongoPort = $_ENV['MONGO_PORT'];
$mongoDb   = $_ENV['MONGO_DB'];
$mongoAuth = $_ENV['MONGO_AUTH_DB'];

$mongoUri = "mongodb://{$mongoUser}:{$mongoPass}@{$mongoHost}:{$mongoPort}/{$mongoDb}?authSource={$mongoAuth}";

// debug - 記錄連線字串
error_log("MongoDB 連線字串: " . $mongoUri);

$mongodb_connected = false;
$mongodb_error = '';
$mongodb_details = [];
$client = null;
$collection = null;

try {
    $client = new MongoDB\Client($mongoUri);

    // 詳細測試連接
    $databases = $client->listDatabases();
    $mongodb_details['server_info'] = $client->getManager()->getServers()[0] ?? null;

    // 檢查目標數據庫是否存在
    $database_exists = false;
    foreach ($databases as $database) {
        if ($database->getName() === $mongoDb) {
            $database_exists = true;
            break;
        }
    }
    $mongodb_details['database_exists'] = $database_exists;
    $mongodb_details['target_database'] = $mongoDb;

    // 獲取數據庫信息
    $db = $client->$mongoDb;
    $collections = $db->listCollections();
    $collection_names = [];
    foreach ($collections as $collection_info) {
        $collection_names[] = $collection_info->getName();
    }
    $mongodb_details['collections'] = $collection_names;
    $mongodb_details['users_collection_exists'] = in_array('users', $collection_names);

    // 設置 users 集合
    $collection = $client->$mongoDb->users;

    // 測試集合操作權限
    try {
        $user_count = $collection->countDocuments();
        $mongodb_details['user_count'] = $user_count;
        $mongodb_details['can_read'] = true;
    } catch (Exception $e) {
        $mongodb_details['can_read'] = false;
        $mongodb_details['read_error'] = $e->getMessage();
    }

    // 測試寫入權限（嘗試插入一個測試文檔然後立即刪除）
    try {
        $test_doc = ['_test' => true, 'timestamp' => new MongoDB\BSON\UTCDateTime()];
        $insert_result = $collection->insertOne($test_doc);
        $collection->deleteOne(['_id' => $insert_result->getInsertedId()]);
        $mongodb_details['can_write'] = true;
    } catch (Exception $e) {
        $mongodb_details['can_write'] = false;
        $mongodb_details['write_error'] = $e->getMessage();
    }

    $mongodb_connected = true;
    error_log("MongoDB 連接成功 - 數據庫: $mongoDb, 用戶數: " . ($mongodb_details['user_count'] ?? 'unknown'));

} catch (Exception $e) {
    $mongodb_error = 'MongoDB connection failed: ' . $e->getMessage();
    error_log($mongodb_error);
}

$file = '../users.json';
$success_message = '';
$error_message = '';
$users_per_page = isset($_GET['users_per_page']) ? (int)$_GET['users_per_page'] : 25;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$filter_status = isset($_GET['status']) ? $_GET['status'] : '';
$search_email = isset($_GET['search_email']) ? $_GET['search_email'] : '';

// 如果 MongoDB 連接失敗，顯示錯誤信息
if (!$mongodb_connected) {
    $error_message = $mongodb_error;
}

// 數據讀取 - 支持 MongoDB 和 JSON 文件雙重模式
if ($mongodb_connected) {
    // 使用 MongoDB
    try {
        // 構建 MongoDB 查詢條件
        $filter = [];

        if ($filter_status) {
            $filter['status'] = $filter_status;
        }

        if ($search_email) {
            $filter['email'] = ['$regex' => $search_email, '$options' => 'i']; // 不區分大小寫搜索
        }

        // 獲取總用戶數
        $total_users = $collection->countDocuments($filter);
        $total_pages = ceil($total_users / $users_per_page);
        $start_index = ($page - 1) * $users_per_page;

        // 獲取分頁數據
        $cursor = $collection->find($filter, [
            'skip' => $start_index,
            'limit' => $users_per_page,
            'sort' => ['email' => 1] // 按郵箱排序
        ]);

        $paginated_data = [];
        foreach ($cursor as $doc) {
            $user = $doc->toArray();
            $paginated_data[] = $user;
        }

    } catch (Exception $e) {
        $error_message = 'MongoDB query failed: ' . $e->getMessage();
        $paginated_data = [];
        $total_users = 0;
        $total_pages = 0;
    }
} else {
    // 回退到 JSON 文件模式
    $json = file_get_contents($file);
    $data = json_decode($json, true);

    if ($filter_status) {
        $data = array_filter($data, function($user) use ($filter_status) {
            return isset($user['status']) && $user['status'] === $filter_status;
        });
    }

    if ($search_email) {
        $data = array_filter($data, function($user) use ($search_email) {
            return isset($user['email']) && strpos($user['email'], $search_email) !== false;
        });
    }

    $total_users = count($data);
    $total_pages = ceil($total_users / $users_per_page);
    $start_index = ($page - 1) * $users_per_page;
    $paginated_data = array_slice($data, $start_index, $users_per_page);
}

// 删除用户
if (isset($_POST['delete'])) {
    $emailToDelete = $_POST['email'];

    if ($mongodb_connected) {
        // 使用 MongoDB
        try {
            $result = $collection->deleteOne(['email' => $emailToDelete]);
            if ($result->getDeletedCount() > 0) {
                $success_message = 'User deleted successfully: ' . htmlspecialchars($emailToDelete);
            } else {
                $error_message = 'User not found: ' . htmlspecialchars($emailToDelete);
            }
        } catch (Exception $e) {
            $error_message = 'Delete operation failed: ' . $e->getMessage();
        }
    } else {
        // 回退到 JSON 文件模式
        $json = file_get_contents($file);
        $data = json_decode($json, true);
        foreach ($data as $key => $user) {
            if ($user['email'] == $emailToDelete) {
                unset($data[$key]);
            }
        }
        file_put_contents($file, json_encode(array_values($data), JSON_PRETTY_PRINT));
        $success_message = 'User deleted successfully: ' . htmlspecialchars($emailToDelete);
    }

    header("Location: " . $_SERVER['PHP_SELF']);
    exit;
}

// 修改密码
if (isset($_POST['change_password'])) {
    $emailToChange = $_POST['email'];
    $newPassword = $_POST['new_password'];
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{9,}$/', $newPassword)) {
        $error_message = 'Error: Password must be at least 9 characters long, contain uppercase and lowercase letters, a number, and a special character.';
    } else {
        $newPasswordHashed = password_hash($newPassword, PASSWORD_DEFAULT);

        if ($mongodb_connected) {
            // 使用 MongoDB
            try {
                $result = $collection->updateOne(
                    ['email' => $emailToChange],
                    ['$set' => ['password' => $newPasswordHashed]]
                );
                if ($result->getModifiedCount() > 0) {
                    $success_message = 'Password changed successfully for ' . htmlspecialchars($emailToChange);
                } else {
                    $error_message = 'User not found or password unchanged: ' . htmlspecialchars($emailToChange);
                }
            } catch (Exception $e) {
                $error_message = 'Password update failed: ' . $e->getMessage();
            }
        } else {
            // 回退到 JSON 文件模式
            $json = file_get_contents($file);
            $data = json_decode($json, true);
            foreach ($data as &$user) {
                if ($user['email'] == $emailToChange) {
                    $user['password'] = $newPasswordHashed;
                }
            }
            file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
            $success_message = 'Password changed successfully for ' . htmlspecialchars($emailToChange);
        }
    }
}

// 锁定用户
if (isset($_POST['lock_user'])) {
    $emailToLock = $_POST['email'];

    if ($mongodb_connected) {
        // 使用 MongoDB
        try {
            $result = $collection->updateOne(
                ['email' => $emailToLock],
                ['$set' => ['status' => 'locked']]
            );
            if ($result->getModifiedCount() > 0) {
                $success_message = 'User locked successfully: ' . htmlspecialchars($emailToLock);
            } else {
                $error_message = 'User not found: ' . htmlspecialchars($emailToLock);
            }
        } catch (Exception $e) {
            $error_message = 'Lock operation failed: ' . $e->getMessage();
        }
    } else {
        // 回退到 JSON 文件模式
        $json = file_get_contents($file);
        $data = json_decode($json, true);
        foreach ($data as &$user) {
            if ($user['email'] == $emailToLock) {
                $user['status'] = 'locked';
            }
        }
        file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
        $success_message = 'User locked successfully: ' . htmlspecialchars($emailToLock);
    }
}

// 解锁用户
if (isset($_POST['unlock_user'])) {
    $emailToUnlock = $_POST['email'];

    if ($mongodb_connected) {
        // 使用 MongoDB
        try {
            $result = $collection->updateOne(
                ['email' => $emailToUnlock],
                ['$set' => ['status' => 'active']]
            );
            if ($result->getModifiedCount() > 0) {
                $success_message = 'User unlocked successfully: ' . htmlspecialchars($emailToUnlock);
            } else {
                $error_message = 'User not found: ' . htmlspecialchars($emailToUnlock);
            }
        } catch (Exception $e) {
            $error_message = 'Unlock operation failed: ' . $e->getMessage();
        }
    } else {
        // 回退到 JSON 文件模式
        $json = file_get_contents($file);
        $data = json_decode($json, true);
        foreach ($data as &$user) {
            if ($user['email'] == $emailToUnlock) {
                $user['status'] = 'active';
            }
        }
        file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
        $success_message = 'User unlocked successfully: ' . htmlspecialchars($emailToUnlock);
    }
}

// 更新备注
if (isset($_POST['update_remark'])) {
    $emailToUpdate = $_POST['email'];
    $newRemark = $_POST['remark'];

    if ($mongodb_connected) {
        // 使用 MongoDB
        try {
            $result = $collection->updateOne(
                ['email' => $emailToUpdate],
                ['$set' => ['remark' => $newRemark]]
            );
            if ($result->getModifiedCount() > 0) {
                $success_message = 'Remark updated successfully for ' . htmlspecialchars($emailToUpdate);
            } else {
                $error_message = 'User not found: ' . htmlspecialchars($emailToUpdate);
            }
        } catch (Exception $e) {
            $error_message = 'Remark update failed: ' . $e->getMessage();
        }
    } else {
        // 回退到 JSON 文件模式
        $json = file_get_contents($file);
        $data = json_decode($json, true);
        foreach ($data as &$user) {
            if ($user['email'] == $emailToUpdate) {
                $user['remark'] = $newRemark;
            }
        }
        file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
        $success_message = 'Remark updated successfully for ' . htmlspecialchars($emailToUpdate);
    }
}

// 更新角色
if (isset($_POST['update_role'])) {
    $emailToUpdate = $_POST['email'];
    $newRole = $_POST['role'];

    if ($mongodb_connected) {
        // 使用 MongoDB
        try {
            $result = $collection->updateOne(
                ['email' => $emailToUpdate],
                ['$set' => ['role' => $newRole]]
            );
            if ($result->getModifiedCount() > 0) {
                $success_message = 'Role updated successfully for ' . htmlspecialchars($emailToUpdate);
            } else {
                $error_message = 'User not found: ' . htmlspecialchars($emailToUpdate);
            }
        } catch (Exception $e) {
            $error_message = 'Role update failed: ' . $e->getMessage();
        }
    } else {
        // 回退到 JSON 文件模式
        $json = file_get_contents($file);
        $data = json_decode($json, true);
        foreach ($data as &$user) {
            if ($user['email'] == $emailToUpdate) {
                $user['role'] = $newRole;
            }
        }
        file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
        $success_message = 'Role updated successfully for ' . htmlspecialchars($emailToUpdate);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
       

    .password-input {
        width: 150px; /* 調整寬度 */
        box-sizing: border-box;
        padding-right: 10px; /* 調整右側間距 */
    }
    .password-form {
        display: inline-block;
        position: relative;
        width: auto; /* 自動調整寬度 */
    }


.toggle-password {
    position: absolute;
    right: -50px; /* 調整右側位置，靠近密碼欄位 */
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #007bff; /* 蓝色文本 */
    background: none;
    border: none;
    font-size: 14px;
    padding: 0;
}

 
        .change-password-button {
            background-color: black;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            margin-top: 5px; /* 调整为与输入框对齐 */
        }
        .change-password-button:hover {
            background-color: #333;
        }
        .toggle-password:hover {
            text-decoration: underline;
        }
        .lock-button, .unlock-button {
            background-color: #f44336;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
        }
        .unlock-button {
            background-color: #4CAF50;
        }
        .lock-button:hover, .unlock-button:hover {
            background-color: #d32f2f;
        }
        .unlock-button:hover {
            background-color: #45a049;
        }
        .success-message, .error-message {
            color: green;
            margin-top: 10px;
            text-align: center; /* 将消息居中 */
        }
        .error-message {
            color: red;
        }
        .remark-input {
            width: 80%;
            box-sizing: border-box;
        }
        .remark-form {
            display: inline-block;
            position: relative;
            width: 100%;
        }
        .filter-container {
            display: flex;
            align-items: center;
            justify-content: flex-end; /* 右对齐 */
            margin-bottom: 10px;
        }
        .filter-container button {
            background-color: #ccc;
            color: black;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            margin: 0 2px;
        }
        .filter-container button[disabled] {
            background-color: #999;
            color: white;
        }
        .filter-container .refresh-icon {
            font-size: 20px;
            cursor: pointer;
            margin-left: 10px;
        }
        .search-container {
            text-align: left;
            margin-bottom: 10px;
        }

        .pagination-container {
            display: flex;
            justify-content: flex-end; /* 调整为右对齐 */
            align-items: center;
            margin-top: 10px;
        }

        .pagination-container form {
            display: inline;
        }
        .pagination-container a {
            margin: 0 2px;
            text-decoration: none;
        }

        .add-user-button {
            background-color: blue;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            margin-bottom: 10px;
        }
        .add-user-button:hover {
            background-color: #0056b3;
        }
        .copy-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            margin-left: 5px;
            transition: background-color 0.3s;
        }
        .copy-button.copied {
            background-color: #007bff; /* 复制后变成蓝色 */
        }
        .copy-button:hover {
            background-color: #45a049;
        }
        .copy-message {
            visibility: hidden;
            min-width: 200px;
            background-color: #555;
            color: white;
            text-align: center;
            border-radius: 5px;
            padding: 10px 0;
            position: fixed;
            z-index: 1;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 17px;
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
        .copy-message.show {
            visibility: visible;
            opacity: 1;
        }

        .otpauth-url {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px; /* 调整这个值来设置显示的最大宽度 */
            display: inline-block;
            vertical-align: middle;
            position: relative; /* 修改为相对定位 */
        }

        .otpauth-url:hover::after {
            content: attr(title); /* 使用 title 属性的内容 */
            white-space: normal;
            background-color: #222;
            color: #fff;
            padding: 5px;
            border: 1px solid #ccc;
            position: absolute;
            top: 100%; /* 显示在下方 */
            left: 0;
            z-index: 1000;
            max-width: none; /* 确保完整显示 */
            white-space: nowrap;
            box-shadow: 0px 8px 16px rgba(0,0,0,0.2);
        }
        .pagination {
            display: flex;
            align-items: center;
        }
        .pagination button {
            background-color: #fff;
            border: 1px solid #ccc;
            padding: 5px 10px;
            cursor: pointer;
        }
        .pagination button.disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }
        .pagination .page-numbers {
            display: flex;
            margin: 0 5px;
        }
        .pagination .page-numbers a {
            margin: 0 2px;
            text-decoration: none;
            padding: 5px 10px;
            border: 1px solid #ccc;
        }
        .pagination .page-numbers a.active {
            background-color: #007bff;
            color: white;
            border: 1px solid #007bff;
        }
        .status-locked {
            color: red;
        }
    </style>
</head>
<body>
    <h1>User Management</h1>

    <!-- MongoDB 連接狀態顯示 -->
    <div class="database-status" style="margin-bottom: 20px; padding: 15px; border-radius: 5px; <?php echo $mongodb_connected ? 'background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724;' : 'background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'; ?>">
        <strong>Database Status:</strong>
        <?php if ($mongodb_connected): ?>
            <i class="fas fa-check-circle"></i> MongoDB Connected Successfully
            <div style="margin-top: 10px; font-size: 14px;">
                <strong>Connection Details:</strong>
                <ul style="margin: 5px 0; padding-left: 20px;">
                    <li><strong>Database:</strong> <?php echo htmlspecialchars($mongodb_details['target_database']); ?></li>
                    <li><strong>Database Exists:</strong> <?php echo $mongodb_details['database_exists'] ? '✓ Yes' : '✗ No'; ?></li>
                    <li><strong>Users Collection Exists:</strong> <?php echo $mongodb_details['users_collection_exists'] ? '✓ Yes' : '✗ No'; ?></li>
                    <li><strong>User Count:</strong> <?php echo isset($mongodb_details['user_count']) ? $mongodb_details['user_count'] : 'N/A'; ?></li>
                    <li><strong>Read Permission:</strong> <?php echo $mongodb_details['can_read'] ? '✓ Yes' : '✗ No'; ?></li>
                    <li><strong>Write Permission:</strong> <?php echo $mongodb_details['can_write'] ? '✓ Yes' : '✗ No'; ?></li>
                    <?php if (!empty($mongodb_details['collections'])): ?>
                    <li><strong>Available Collections:</strong> <?php echo implode(', ', $mongodb_details['collections']); ?></li>
                    <?php endif; ?>
                </ul>
                <?php if (!$mongodb_details['can_read']): ?>
                <div style="color: #856404; background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 5px; margin-top: 5px; border-radius: 3px;">
                    <strong>Read Error:</strong> <?php echo htmlspecialchars($mongodb_details['read_error'] ?? 'Unknown error'); ?>
                </div>
                <?php endif; ?>
                <?php if (!$mongodb_details['can_write']): ?>
                <div style="color: #856404; background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 5px; margin-top: 5px; border-radius: 3px;">
                    <strong>Write Error:</strong> <?php echo htmlspecialchars($mongodb_details['write_error'] ?? 'Unknown error'); ?>
                </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <i class="fas fa-exclamation-triangle"></i> MongoDB Connection Failed - Using JSON File Fallback
            <div style="margin-top: 10px;">
                <strong>Error Details:</strong>
                <div style="background-color: rgba(255,255,255,0.1); padding: 10px; border-radius: 3px; margin-top: 5px; font-family: monospace; font-size: 12px;">
                    <?php echo htmlspecialchars($mongodb_error); ?>
                </div>
                <div style="margin-top: 10px; font-size: 14px;">
                    <strong>Connection Attempted:</strong>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li><strong>Host:</strong> <?php echo htmlspecialchars($mongoHost); ?></li>
                        <li><strong>Port:</strong> <?php echo htmlspecialchars($mongoPort); ?></li>
                        <li><strong>Database:</strong> <?php echo htmlspecialchars($mongoDb); ?></li>
                        <li><strong>Auth Database:</strong> <?php echo htmlspecialchars($mongoAuth); ?></li>
                        <li><strong>Username:</strong> <?php echo htmlspecialchars($mongoUser); ?></li>
                    </ul>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="success-message"><?php echo $success_message; ?></div>
    <?php endif; ?>
    <?php if (!empty($error_message)): ?>
        <div class="error-message"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <button class="add-user-button" onclick="openRegisterWindow()">Add User</button>

    <div class="search-container">
        <form method="GET" action="">
            <label for="search_email">Search Email:</label>
            <input type="text" id="search_email" name="search_email" value="<?php echo htmlspecialchars($search_email); ?>">
            <input type="submit" value="Search">
        </form>
    </div>

    <div class="filter-container">
        <form method="GET" action="">
            <input type="hidden" name="users_per_page" value="<?php echo $users_per_page; ?>">
            <span>Filter by status:</span>
            <button type="submit" name="status" value="active" <?php if ($filter_status == 'active') echo 'disabled'; ?>>Active</button>
            <button type="submit" name="status" value="locked" <?php if ($filter_status == 'locked') echo 'disabled'; ?>>Locked</button>
            <button type="submit" name="status" value="" <?php if ($filter_status == '') echo 'disabled'; ?>>All</button>
        </form>
        <i class="fas fa-sync-alt refresh-icon" onclick="location.reload();"></i>
    </div>

    <table>
        <thead>
            <tr>
                <th>Email</th>
                <th>Password</th>
                <th>Issuer</th>
                <th>otpauth URL</th>
                <th>Status</th>
                <th>Remark</th>
                <th>Role</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($paginated_data as $user): ?>
            <tr>
                <td><?php echo htmlspecialchars($user['email']); ?></td>
                <td>
                    <form class="password-form" method="post" onsubmit="return confirmChangePassword()">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <input type="password" name="new_password" class="password-input" placeholder="New Password" required>
                        <button type="button" class="toggle-password" onclick="togglePasswordVisibility(this)">顯示</button>
                        <button type="submit" name="change_password" class="change-password-button">Change</button>
                    </form>
                </td>
                <td><?php echo htmlspecialchars($user['issuer']); ?></td>
                <td>
                    <div class="otpauth-url" title="<?php echo htmlspecialchars($user['otpauth_url']); ?>">
                        <?php echo htmlspecialchars($user['otpauth_url']); ?>
                    </div>
                    <button class="copy-button" onclick="copyToClipboard('otpauth-<?php echo htmlspecialchars($user['email']); ?>', this)">Copy</button>
                    <span id="otpauth-<?php echo htmlspecialchars($user['email']); ?>" style="display:none;"><?php echo htmlspecialchars($user['otpauth_url']); ?></span>
                </td>
                <td class="<?php echo isset($user['status']) && $user['status'] == 'locked' ? 'status-locked' : ''; ?>"><?php echo isset($user['status']) ? htmlspecialchars($user['status']) : ''; ?></td>
                <td>
                    <form class="remark-form" method="post" onsubmit="return updateRemark(this)">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <input type="text" name="remark" class="remark-input" value="<?php echo isset($user['remark']) ? htmlspecialchars($user['remark']) : ''; ?>">
                        <button type="submit" name="update_remark" class="change-password-button">Update</button>
                    </form>
                </td>
                <td>
                    <form class="role-form" method="post" onsubmit="return updateRole(this)">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <select name="role">
                            <option value="user" <?php if (isset($user['role']) && $user['role'] == 'user') echo 'selected'; ?>>User</option>
                            <option value="admin" <?php if (isset($user['role']) && $user['role'] == 'admin') echo 'selected'; ?>>Admin</option>
                        </select>
                        <button type="submit" name="update_role" class="change-password-button">Update</button>
                    </form>
                </td>
                <td>
                    <form method="post" style="display:inline;" onsubmit="return updateStatus(this, '<?php echo isset($user['status']) && $user['status'] == 'active' ? 'lock_user' : 'unlock_user'; ?>')">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <?php if (isset($user['status']) && $user['status'] == 'active'): ?>
                            <button type="submit" name="lock_user" class="lock-button">Lock</button>
                        <?php else: ?>
                            <button type="submit" name="unlock_user" class="unlock-button">Unlock</button>
                        <?php endif; ?>
                    </form>
                    <form method="post" style="display:inline;" onsubmit="return confirmDelete()">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <button type="submit" name="delete">Delete</button>
                    </form>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <div class="pagination-container">
        <div>共 <?php echo $total_users; ?> 条</div>
        <div class="pagination">
            <button class="previous" onclick="changePage(<?php echo $page - 1; ?>)" <?php if ($page <= 1) echo 'disabled'; ?>>&lt;</button>
            <div class="page-numbers">
                <?php for ($i = max(1, $page - 1); $i <= min($total_pages, $page + 1); $i++): ?>
                    <a href="?page=<?php echo $i; ?>&users_per_page=<?php echo $users_per_page; ?>&status=<?php echo $filter_status; ?>&search_email=<?php echo htmlspecialchars($search_email); ?>" class="<?php if ($i == $page) echo 'active'; ?>"><?php echo $i; ?></a>
                <?php endfor; ?>
            </div>
            <button class="next" onclick="changePage(<?php echo $page + 1; ?>)" <?php if ($page >= $total_pages) echo 'disabled'; ?>>&gt;</button>
            <select id="users_per_page" name="users_per_page" onchange="changeUsersPerPage(this.value)">
                <option value="5" <?php if ($users_per_page == 5) echo 'selected'; ?>>5条/页</option>
                <option value="10" <?php if ($users_per_page == 10) echo 'selected'; ?>>10条/页</option>
                <option value="25" <?php if ($users_per_page == 25) echo 'selected'; ?>>25条/页</option>
                <option value="50" <?php if ($users_per_page == 50) echo 'selected'; ?>>50条/页</option>
            </select>
        </div>
    </div>

    <div id="copy-message" class="copy-message">Copied to clipboard</div>

    <script>
        function confirmDelete() {
            return confirm('Are you sure you want to delete this user?');
        }

        function openRegisterWindow() {
            window.open('../register.php', 'Register User', 'width=500,height=600');
        }

        function confirmChangePassword() {
            return confirm('Are you sure you want to change this user\'s password?');
        }

        function confirmLockUser() {
            return confirm('Are you sure you want to lock this user?');
        }

        function togglePasswordVisibility(button) {
            var passwordInput = button.previousElementSibling;
            if (passwordInput.type === "password") {
                passwordInput.type = "text";
                button.innerText = "隱藏";
            } else {
                passwordInput.type = "password";
                button.innerText = "顯示";
            }
        }

        function copyToClipboard(elementId, button) {
            var copyText = document.getElementById(elementId);
            var textarea = document.createElement("textarea");
            textarea.value = copyText.innerText;
            document.body.appendChild(textarea);
            textarea.select();
            try {
                document.execCommand('copy');
                button.classList.add('copied');
                setTimeout(() => {
                    button.classList.remove('copied');
                }, 2000);
                showCopyMessage();
            } catch (err) {
                alert('Failed to copy');
            }
            document.body.removeChild(textarea);
        }

        function showCopyMessage() {
            var copyMessage = document.getElementById("copy-message");
            copyMessage.classList.add("show");
            setTimeout(function() {
                copyMessage.classList.remove("show");
            }, 2000);
        }

        function changePage(page) {
            if (page < 1 || page > <?php echo $total_pages; ?>) return;
            const params = new URLSearchParams(window.location.search);
            params.set('page', page);
            window.location.search = params.toString();
        }

        function changeUsersPerPage(value) {
            const params = new URLSearchParams(window.location.search);
            params.set('users_per_page', value);
            params.set('page', 1); // Reset to first page
            window.location.search = params.toString();
        }

        function updateRole(form) {
            var formData = new FormData(form);
            formData.append('update_role', 'update_role');
            fetch('', {
                method: 'POST',
                body: formData
            }).then(response => response.text()).then(data => {
                location.reload(); // 成功更新后刷新页面
            });
            return false;
        }

        function updateRemark(form) {
            var formData = new FormData(form);
            formData.append('update_remark', 'update_remark');
            fetch('', {
                method: 'POST',
                body: formData
            }).then(response => response.text()).then(data => {
                location.reload(); // 成功更新后刷新页面
            });
            return false;
        }

        function updateStatus(form, action) {
            var formData = new FormData(form);
            formData.append(action, action);
            fetch('', {
                method: 'POST',
                body: formData
            }).then(response => response.text()).then(data => {
                location.reload(); // 成功更新后刷新页面
            });
            return false;
        }
    </script>
</body>
</html>


