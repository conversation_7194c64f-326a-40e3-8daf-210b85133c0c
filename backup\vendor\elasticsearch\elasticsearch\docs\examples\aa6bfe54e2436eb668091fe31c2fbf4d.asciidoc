// aggregations/bucket/datehistogram-aggregation.asciidoc:502

[source, php]
----
$params = [
    'index' => 'my_index',
    'id' => '1',
    'body' => [
        'date' => '2015-10-01T05:30:00Z',
    ],
];
$response = $client->index($params);
$params = [
    'index' => 'my_index',
    'id' => '2',
    'body' => [
        'date' => '2015-10-01T06:30:00Z',
    ],
];
$response = $client->index($params);
$params = [
    'index' => 'my_index',
    'body' => [
        'aggs' => [
            'by_day' => [
                'date_histogram' => [
                    'field' => 'date',
                    'calendar_interval' => 'day',
                    'offset' => '+6h',
                ],
            ],
        ],
    ],
];
$response = $client->search($params);
----
